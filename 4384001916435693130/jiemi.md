### 32位仿射变换密码分析流程

#### 摘要

该算法是一个在有限域 GF(2) 上的32位仿射变换。其数学模型为：

`y = (M · x) ⊕ C`

-   `x`: 32位输入向量。
-   `y`: 32位输出向量。
-   `M`: 32x32 的二进制变换矩阵（线性部分）。
-   `C`: 32位的常数向量（平移部分）。
-   `⊕`: 按位异或（XOR）运算。
-   `·`: GF(2) 上的矩阵乘法（由按位与和异或构成）。

解密流程分为两步：首先求解线性部分矩阵 `M`，然后求解常数 `C`。

---

### 第1步：线性化变换，消除常数 C

为了求解矩阵 `M`，我们必须先消除常数 `C` 的影响。利用 XOR 的性质 `A ⊕ A = 0`，可以实现线性化。

1.  任取两个输入/输出对：
    -   `y₁ = (M · x₁) ⊕ C`
    -   `y₂ = (M · x₂) ⊕ C`

2.  将两个方程进行异或：
    `y₁ ⊕ y₂ = ((M · x₁) ⊕ C) ⊕ ((M · x₂) ⊕ C)`
    `y₁ ⊕ y₂ = M · x₁ ⊕ M · x₂ ⊕ C ⊕ C`
    `y₁ ⊕ y₂ = M · (x₁ ⊕ x₂)`

3.  定义新的变量：
    -   `x' = x₁ ⊕ x₂`
    -   `y' = y₁ ⊕ y₂`

4.  我们得到一个纯线性方程：`y' = M · x'`。
    现在，问题转化为：根据已知的 `x'` 和 `y'` 对，求解未知的矩阵 `M`。

---

### 第2步：构建并求解线性方程组

矩阵 `M` 是一个 32x32 的矩阵，共有 `32 * 32 = 1024` 个未知的比特。我们需要足够的线性方程来求解它。

1.  **生成数据**：从给定的 `N` 个输入/输出对中，任取两对进行异或，可以生成 `C(N, 2)` 个 `(x', y')` 线性关系对。

2.  **建立方程**：
    -   `y' = M · x'` 的计算可以分解到每个比特位。输出 `y'` 的第 `i` 位 (`y'_i`) 等于矩阵 `M` 的第 `i` 行（`M_row_i`）与向量 `x'` 的点积。
    -   在 GF(2) 中，点积 `M_row_i · x'` 等价于 `parity(M_row_i & x')`，即计算 `M_row_i` 和 `x'` 按位与之后结果中 `1` 的数量的奇偶性。
    -   因此，我们有 `y'_i = parity(M_row_i & x')`。

3.  **求解矩阵 `M` 的每一行**：
    -   为了求解 `M` 的一行（例如 `M_row_k`，它是一个32位的未知数），我们需要32个关于它的线性方程。
    -   这意味着我们需要从生成的 `x'` 集合中，找到32个**线性无关**的向量，组成一个基。设这个基为 `{x'_0, x'_1, ..., x'_31}`，它们对应的输出为 `{y'_0, y'_1, ..., y'_31}`。
    -   针对 `M_row_k`，我们建立如下 32x32 的方程组：
      ```
      M_row_k · x'_0 = (k-th bit of y'_0)
      M_row_k · x'_1 = (k-th bit of y'_1)
      ...
      M_row_k · x'_31 = (k-th bit of y'_31)
      ```
    -   这是一个标准的 `A·z = b` 形式的线性方程组，其中 `A` 的行是 `x'` 基向量，`z` 是待求解的 `M_row_k`，`b` 是对应的输出比特。
    -   使用**高斯消元法**在 GF(2) 上求解该方程组，即可得到 `M_row_k` 的值。

4.  **重复求解**：重复步骤3共32次（从 `k=0` 到 `k=31`），即可求出矩阵 `M` 的所有32行。这32个32位整数就是Python代码中的 `M_ROWS` 常量。

---

### 第3步：求解常数 C

在求得矩阵 `M` 后，求解 `C` 非常简单。

1.  回到原始仿射变换公式：`y = (M · x) ⊕ C`。
2.  移项得到：`C = y ⊕ (M · x)`。
3.  任取一个原始的输入/输出对（例如，第一个 `x = 0xd22a86af`, `y = 0x88748e76`）。
4.  使用已求得的矩阵 `M` 计算 `M · x` 的值。这个计算过程就是代码中 `transform` 函数的核心循环：
   ```python
   # 伪代码计算 M·x
   mx = 0
   for i, m_row in enumerate(M_ROWS):
       if parity(x & m_row):
           mx ^= (1 << i)
   ```
5.  代入公式计算 `C`：
    `C = y ⊕ mx`
    `C = 0x88748e76 ⊕ (计算出的 M·x 的结果)`

    计算结果应为 `0x6500001D`。

---

### 第4步：验证与实现

1.  **整合**：将求得的 `M_ROWS` 和 `C` 放入最终的实现代码中。

2.  **编写函数**：实现 `transform(x)` 函数，它精确地执行 `y = (M · x) ⊕ C` 的运算。
   ```python
   def transform(x):
       y = C # 初始化 y 为常数 C
       for i, mask in enumerate(M_ROWS): # mask 是 M 的第 i 行
           # _parity(x & mask) 计算 M·x 的第 i 位
           if _parity(x & mask):
               # 将 M·x 的第 i 位异或到 y 上
               y ^= (1 << i)
       return y & 0xFFFFFFFF
   ```

3.  **验证**：使用所有给定的输入/输出对测试 `transform` 函数，确保计算结果完全匹配。如果全部匹配，则证明解密成功。