from django.db.models import Avg, Count, Sum
from models import ElevatorData


def calculate_kpis(elevator_id: str) -> dict:
    """计算关键性能指标（KPIs）"""
    qs = ElevatorData.objects.filter(elevator__elevator_id=elevator_id)

    total = qs.count()
    fault_total = qs.filter(fault_code__gt=0).count()
    fault_rate = fault_total / total if total else 0

    # 按小时平均启动/停止次数
    hourly = qs.extra({"hour": "strftime('%H', timestamp)"}).values("hour").annotate(
        avg_start=Avg("start_count"), avg_stop=Avg("stop_count")
    ).order_by("hour")

    # 各故障类型分布
    distribution = qs.filter(fault_code__gt=0).values("fault_code").annotate(
        count=Count("id"),
        percentage=100.0 * Count("id") / fault_total if fault_total else 0,
    )

    return {
        "fault_rate": round(fault_rate * 100, 2),    # %
        "hourly_stats": list(hourly),
        "fault_distribution": list(distribution),
        "total_operations": qs.aggregate(
            total_start=Sum("start_count"),
            total_stop=Sum("stop_count"),
        ),
    }
