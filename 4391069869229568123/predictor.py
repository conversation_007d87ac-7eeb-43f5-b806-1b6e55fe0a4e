import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# 尝试导入TensorFlow，如果失败则使用简化版本
try:
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.callbacks import EarlyStopping
    TENSORFLOW_AVAILABLE = True
except ImportError:
    try:
        # 尝试旧版本的导入方式
        from keras.models import Sequential
        from keras.layers import LSTM, Dense, Dropout
        from keras.callbacks import EarlyStopping
        TENSORFLOW_AVAILABLE = True
    except ImportError:
        # 如果都导入失败，使用简化版本
        TENSORFLOW_AVAILABLE = False
        print("警告: TensorFlow/Keras 不可用，将使用简化的预测模型")


class FaultPredictor:
    def __init__(self, time_steps: int = 30):
        self.time_steps = time_steps          # 连续 30 个时间点
        self.scaler = StandardScaler()
        self.model = self._build_model() if TENSORFLOW_AVAILABLE else None

    def _build_model(self):
        if not TENSORFLOW_AVAILABLE:
            return None

        model = Sequential([
            LSTM(64, input_shape=(self.time_steps, 5), return_sequences=True),
            Dropout(0.2),
            LSTM(32),
            Dense(1, activation="sigmoid")
        ])
        model.compile(
            loss="binary_crossentropy",
            optimizer="adam",
            metrics=["accuracy"],
        )
        return model

    # ---------- 数据预处理 ----------
    def preprocess_data(self, data: np.ndarray):
        """
        参数 data 形状：(N, 5)，列分别为
        [start_count, stop_count, run_time, temperature, voltage]
        """
        scaled = self.scaler.fit_transform(data)

        X, y = [], []
        for i in range(len(scaled) - self.time_steps):
            X.append(scaled[i:i + self.time_steps])
            # 用 voltage 列是否非零作为是否故障的简单示例
            y.append(1 if data[i + self.time_steps][4] > 0 else 0)

        return np.array(X), np.array(y)

    # ---------- 训练 ----------
    def train(self, X, y, epochs=50, batch_size=32):
        if not TENSORFLOW_AVAILABLE or self.model is None:
            print("TensorFlow不可用，跳过模型训练")
            return None

        X_tr, X_te, y_tr, y_te = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        es = EarlyStopping(monitor="val_loss", patience=5)
        return self.model.fit(
            X_tr, y_tr,
            epochs=epochs,
            batch_size=batch_size,
            validation_data=(X_te, y_te),
            callbacks=[es],
        )

    # ---------- 预测 ----------
    def predict_fault(self, recent_data: np.ndarray) -> float:
        """返回故障概率 (0~1)"""
        if not TENSORFLOW_AVAILABLE or self.model is None:
            # 使用简化的规则预测
            return self._simple_predict(recent_data)

        try:
            # 检查scaler是否已经fitted
            if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None:
                # 如果scaler没有fitted，先用当前数据fit
                self.scaler.fit(recent_data)

            scaled = self.scaler.transform(recent_data)

            if len(scaled) < self.time_steps:
                # 数据不足，使用简化预测
                return self._simple_predict(recent_data)

            input_arr = np.array([scaled[-self.time_steps:]])
            return float(self.model.predict(input_arr)[0][0])
        except Exception as e:
            print(f"LSTM预测失败，使用简化预测: {e}")
            return self._simple_predict(recent_data)

    def _simple_predict(self, recent_data: np.ndarray) -> float:
        """简化的故障预测逻辑"""
        if len(recent_data) == 0:
            return 0.1

        # 获取最新数据
        latest = recent_data[-1] if len(recent_data.shape) == 2 else recent_data

        # 假设数据格式: [start_count, stop_count, run_time, temperature, voltage]
        if len(latest) >= 5:
            temperature = latest[3]
            voltage = latest[4]

            # 基于温度和电压的简单规则
            if temperature > 85 or abs(voltage - 220) > 30:
                return 0.9  # 高风险
            elif temperature > 75 or abs(voltage - 220) > 20:
                return 0.6  # 中风险
            elif temperature > 65 or abs(voltage - 220) > 10:
                return 0.3  # 低风险
            else:
                return 0.1  # 正常

        return 0.1


# ---------------- 使用示例 ----------------
if __name__ == "__main__":
    # 生成模拟数据进行测试
    import random

    print("生成模拟电梯数据进行故障预测测试...")

    # 生成100条模拟数据
    data = []
    for i in range(100):
        start_count = random.randint(10, 50)
        stop_count = start_count + random.randint(-2, 2)
        run_time = start_count * random.randint(30, 120)
        temperature = random.uniform(20, 90)
        voltage = random.uniform(200, 240)

        data.append([start_count, stop_count, run_time, temperature, voltage])

    data = np.array(data)

    try:
        fp = FaultPredictor()

        if TENSORFLOW_AVAILABLE:
            X, y = fp.preprocess_data(data)

            if len(X) > 0:
                print(f"训练数据形状: X={X.shape}, y={y.shape}")
                history = fp.train(X, y, epochs=10)
                if history and fp.model:
                    fp.model.save("fault_predictor_model.h5")
                    print("模型训练完成并保存")
            else:
                print("数据不足，无法训练模型")
        else:
            print("TensorFlow不可用，使用简化预测模型")

        # 测试预测（无论是否有TensorFlow都可以测试）
        if len(data) >= 30:  # LSTM模型需要30个时间点
            test_data = data[-30:]  # 使用最后30条数据
            prob = fp.predict_fault(test_data)
            print(f"故障预测概率: {prob:.3f}")
        elif len(data) >= 5:  # 简化模型只需要最新数据
            test_data = data[-5:]
            prob = fp._simple_predict(test_data)
            print(f"简化预测概率: {prob:.3f}")

            # 测试不同的数据情况
            print("\n测试不同温度和电压的预测结果:")
            test_cases = [
                [20, 20, 1000, 25.0, 220.0],  # 正常
                [30, 30, 1500, 70.0, 215.0],  # 轻微异常
                [40, 40, 2000, 80.0, 200.0],  # 中等风险
                [50, 50, 2500, 90.0, 180.0],  # 高风险
            ]

            for i, case in enumerate(test_cases):
                prob = fp.predict_fault(np.array([case]))
                print(f"测试案例 {i+1}: 温度={case[3]}°C, 电压={case[4]}V, 预测概率={prob:.3f}")
        else:
            print("数据不足，无法进行预测测试")

    except Exception as e:
        print(f"测试过程出错: {e}")
        import traceback
        traceback.print_exc()
