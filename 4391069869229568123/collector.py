import time
import random
import requests
from datetime import datetime


class SimulatedSensor:
    """模拟传感器基类"""
    def __init__(self, base_value, variation_range):
        self.base_value = base_value
        self.variation_range = variation_range
        self.current_value = base_value

    def read(self):
        """读取传感器数据"""
        variation = random.uniform(-self.variation_range, self.variation_range)
        self.current_value = max(0, self.base_value + variation)
        return self.current_value


class MotorSensor:
    """电机传感器模拟"""
    def __init__(self):
        self.start_count = 0
        self.stop_count = 0
        self.total_run_time = 0
        self.last_start_time = None

    def get_start_count(self):
        # 模拟启动次数增加
        if random.random() < 0.1:  # 10%概率增加
            self.start_count += 1
            self.last_start_time = time.time()
        return self.start_count

    def get_stop_count(self):
        # 模拟停止次数
        if self.start_count > self.stop_count and random.random() < 0.08:
            self.stop_count += 1
            if self.last_start_time:
                self.total_run_time += time.time() - self.last_start_time
        return self.stop_count

    def get_run_time(self):
        return int(self.total_run_time)


class TemperatureSensor(SimulatedSensor):
    """温度传感器模拟"""
    def __init__(self):
        super().__init__(base_value=25.0, variation_range=15.0)

    def read(self):
        # 温度在20-90度之间变化
        variation = random.uniform(-5, 20)
        self.current_value = max(20, min(90, self.base_value + variation))
        return round(self.current_value, 1)


class VoltageSensor(SimulatedSensor):
    """电压传感器模拟"""
    def __init__(self):
        super().__init__(base_value=220.0, variation_range=20.0)

    def read(self):
        # 电压在200-240V之间变化
        variation = random.uniform(-15, 15)
        self.current_value = max(200, min(240, self.base_value + variation))
        return round(self.current_value, 1)


class DataCollector:
    def __init__(self, elevator_id: str):
        self.elevator_id = elevator_id
        self.motor_sensor = MotorSensor()
        self.temp_sensor = TemperatureSensor()
        self.voltage_sensor = VoltageSensor()

    def collect_elevator_data(self) -> dict:
        """采集电梯运行数据"""
        temperature = self.temp_sensor.read()
        voltage = self.voltage_sensor.read()

        return {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "elevator_id": self.elevator_id,
            "start_count": self.motor_sensor.get_start_count(),
            "stop_count": self.motor_sensor.get_stop_count(),
            "run_time": self.motor_sensor.get_run_time(),
            "temperature": temperature,
            "voltage": voltage,
            "fault_code": self._detect_fault(temperature, voltage),
        }

    def _detect_fault(self, temperature: float, voltage: float) -> int:
        """故障检测逻辑"""
        if temperature > 85:                     # 温度超过 85 °C
            return 1                            # 电机过热
        if abs(voltage - 220) > 30:             # 电压异常
            return 2                            # 电压异常
        return 0                                # 正常


class DataTransmitter:
    def __init__(self, server_url: str = "http://127.0.0.1:8000"):
        self.server_url = server_url

    def transmit(self, data: dict) -> bool:
        """将数据发送到服务器"""
        try:
            resp = requests.post(
                f"{self.server_url}/api/elevator/data",
                json=data,
                timeout=5,
                headers={'Content-Type': 'application/json'}
            )
            return resp.status_code == 200
        except Exception as exc:
            print(f"数据传输失败: {exc}")
            return False


# ---------------- 使用示例 ----------------
if __name__ == "__main__":
    # 测试数据采集
    collector = DataCollector("E001")
    transmitter = DataTransmitter()

    print("开始电梯数据采集测试...")
    for i in range(5):
        payload = collector.collect_elevator_data()
        print(f"采集数据 {i+1}: {payload}")

        # 可选：发送到服务器
        # if transmitter.transmit(payload):
        #     print("数据发送成功")
        # else:
        #     print("数据发送失败")

        time.sleep(2)  # 每2秒采集一次
