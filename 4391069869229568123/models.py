from django.db import models


class Elevator(models.Model):
    elevator_id = models.CharField(max_length=20, primary_key=True)
    elevator_no = models.CharField(max_length=20)
    location = models.CharField(max_length=100)

    STATUS_CHOICES = [
        ("normal", "正常运行"),
        ("warning", "预警状态"),
        ("fault", "故障状态"),
        ("maintenance", "维护中"),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default="normal"
    )
    installation_date = models.DateField()
    last_maintenance_date = models.DateField()
    maintenance_contact = models.CharField(max_length=100)

    class Meta:
        app_label = 'elevator_monitor'

    def __str__(self):
        return self.elevator_id


class ElevatorData(models.Model):
    elevator = models.ForeignKey(Elevator, on_delete=models.CASCADE)
    timestamp = models.DateTimeField()
    start_count = models.IntegerField()
    stop_count = models.IntegerField()
    run_time = models.IntegerField(help_text="运行时长（秒）")
    temperature = models.FloatField(help_text="温度 °C")
    voltage = models.FloatField(help_text="电压 V")

    FAULT_CHOICES = [
        (0, "无故障"),
        (1, "电机过热"),
        (2, "电压异常"),
        (3, "机械故障"),
        (4, "控制系统故障"),
    ]
    fault_code = models.IntegerField(choices=FAULT_CHOICES, default=0)

    class Meta:
        app_label = 'elevator_monitor'
        indexes = [
            models.Index(fields=["timestamp"]),
            models.Index(fields=["elevator", "timestamp"]),
        ]

    def __str__(self):
        return f"{self.elevator.elevator_id}@{self.timestamp}"
