import json
import random
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Avg
from models import Elevator, ElevatorData
try:
    from predictor import FaultPredictor
    # 创建全局预测器实例
    global_predictor = None
except ImportError:
    FaultPredictor = None
    global_predictor = None
import numpy as np


def get_fault_predictor():
    """获取故障预测器实例"""
    global global_predictor
    if global_predictor is None and FaultPredictor:
        global_predictor = FaultPredictor()
        # 尝试加载预训练模型
        try:
            global_predictor.model.load_weights("fault_predictor_model.h5")
        except:
            pass  # 模型文件不存在，使用简化预测
    return global_predictor


def simple_fault_predict(recent_data):
    """简化的故障预测函数"""
    if len(recent_data) == 0:
        return 0.1

    # 获取最新数据
    latest = recent_data[0] if len(recent_data) > 0 else None
    if latest is None or len(latest) < 5:
        return 0.1

    temperature = latest[3]
    voltage = latest[4]

    # 基于温度和电压的简单规则
    if temperature > 85 or abs(voltage - 220) > 30:
        return 0.9  # 高风险
    elif temperature > 75 or abs(voltage - 220) > 20:
        return 0.6  # 中风险
    elif temperature > 65 or abs(voltage - 220) > 10:
        return 0.3  # 低风险
    else:
        return 0.1  # 正常


@csrf_exempt
def receive_elevator_data(request):
    """接收电梯数据（POST JSON）"""
    if request.method != "POST":
        return JsonResponse({"status": "error", "message": "Invalid request method"})

    try:
        data = json.loads(request.body)
        elevator = Elevator.objects.get(elevator_id=data["elevator_id"])

        # 保存单条数据
        ElevatorData.objects.create(
            elevator=elevator,
            timestamp=data["timestamp"],
            start_count=data["start_count"],
            stop_count=data["stop_count"],
            run_time=data["run_time"],
            temperature=data["temperature"],
            voltage=data["voltage"],
            fault_code=data["fault_code"],
        )

        # 每收到 30 条做一次预测
        recent = ElevatorData.objects.filter(
            elevator=elevator
        ).order_by("-timestamp")[:30].values_list(
            "start_count", "stop_count", "run_time", "temperature", "voltage"
        )

        if len(recent) >= 5:  # 降低数据要求
            try:
                # 使用简化的故障预测
                prob = simple_fault_predict(list(recent))

                if prob >= 0.7:      # 高风险阈值
                    send_alert_notification(elevator, prob)
            except Exception as e:
                print(f"故障预测失败: {e}")

        return JsonResponse({"status": "success"})

    except Exception as exc:
        return JsonResponse({"status": "error", "message": str(exc)})


def get_elevator_stats(request, elevator_id):
    """返回电梯关键统计指标"""
    elevator = Elevator.objects.get(elevator_id=elevator_id)
    qs = ElevatorData.objects.filter(elevator=elevator)

    stats = {
        "avg_start_count": qs.aggregate(Avg("start_count"))["start_count__avg"],
        "avg_stop_count": qs.aggregate(Avg("stop_count"))["stop_count__avg"],
        "fault_rate": qs.filter(fault_code__gt=0).count() / qs.count(),
        "last_24h_data": list(qs.order_by("-timestamp")[:24].values()),
    }
    return JsonResponse(stats)


def send_alert_notification(elevator, probability: float):
    """示例：打印预警，可替换为短信/邮件"""
    print(f"⚠️ 电梯 {elevator.elevator_id} 故障概率：{probability:.2%}")


# ============ 前端页面视图 ============

def index(request):
    """主页面"""
    return render(request, 'index.html')


def elevator_monitor(request):
    """电梯监控主页面"""
    # 确保有4个电梯的基础数据
    elevators = []
    for i in range(1, 5):
        elevator_id = f"E{i:03d}"
        elevator, created = Elevator.objects.get_or_create(
            elevator_id=elevator_id,
            defaults={
                'elevator_no': f"电梯{i}号",
                'location': f"A座{i}号电梯",
                'status': 'normal',
                'installation_date': '2020-01-01',
                'last_maintenance_date': '2024-01-01',
                'maintenance_contact': '维护部门'
            }
        )
        elevators.append(elevator)

    context = {
        'elevators': elevators,
        'total_floors': 10,
    }
    return render(request, 'elevator_monitor.html', context)


# ============ API 接口扩展 ============

def get_all_elevators_status(request):
    """获取所有电梯的状态信息"""
    elevators_status = []

    for i in range(1, 5):
        elevator_id = f"E{i:03d}"
        try:
            elevator = Elevator.objects.get(elevator_id=elevator_id)

            # 获取最新数据
            latest_data = ElevatorData.objects.filter(
                elevator=elevator
            ).order_by('-timestamp').first()

            # 计算基本统计
            recent_data = ElevatorData.objects.filter(
                elevator=elevator,
                timestamp__gte=datetime.now() - timedelta(hours=24)
            )

            fault_count = recent_data.filter(fault_code__gt=0).count()
            total_count = recent_data.count()
            fault_rate = (fault_count / total_count * 100) if total_count > 0 else 0

            status_info = {
                'elevator_id': elevator_id,
                'elevator_no': elevator.elevator_no,
                'status': elevator.status,
                'current_floor': random.randint(1, 10),  # 模拟当前楼层
                'direction': random.choice(['up', 'down', 'idle']),  # 模拟运行方向
                'fault_rate': round(fault_rate, 2),
                'latest_data': {
                    'temperature': latest_data.temperature if latest_data else 25.0,
                    'voltage': latest_data.voltage if latest_data else 220.0,
                    'start_count': latest_data.start_count if latest_data else 0,
                    'stop_count': latest_data.stop_count if latest_data else 0,
                    'fault_code': latest_data.fault_code if latest_data else 0,
                } if latest_data else {
                    'temperature': 25.0,
                    'voltage': 220.0,
                    'start_count': 0,
                    'stop_count': 0,
                    'fault_code': 0,
                }
            }
            elevators_status.append(status_info)

        except Elevator.DoesNotExist:
            # 如果电梯不存在，创建默认状态
            status_info = {
                'elevator_id': elevator_id,
                'elevator_no': f"电梯{i}号",
                'status': 'normal',
                'current_floor': 1,
                'direction': 'idle',
                'fault_rate': 0,
                'latest_data': {
                    'temperature': 25.0,
                    'voltage': 220.0,
                    'start_count': 0,
                    'stop_count': 0,
                    'fault_code': 0,
                }
            }
            elevators_status.append(status_info)

    return JsonResponse({
        'status': 'success',
        'elevators': elevators_status,
        'timestamp': datetime.now().isoformat()
    })


@csrf_exempt
def simulate_fault(request):
    """模拟电梯故障 - 强制破坏数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '只支持POST请求'})

    try:
        data = json.loads(request.body)
        elevator_id = data.get('elevator_id')
        fault_type = data.get('fault_type', 'overheat')  # overheat, voltage, random

        if not elevator_id:
            return JsonResponse({'status': 'error', 'message': '缺少电梯ID'})

        # 获取电梯对象
        try:
            elevator = Elevator.objects.get(elevator_id=elevator_id)
        except Elevator.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': f'电梯 {elevator_id} 不存在'})

        # 根据故障类型生成异常数据
        if fault_type == 'overheat':
            # 电机过热
            temperature = random.uniform(85, 95)
            voltage = random.uniform(215, 225)
            fault_code = 1
            fault_desc = "电机过热"
        elif fault_type == 'voltage':
            # 电压异常
            temperature = random.uniform(25, 35)
            voltage = random.uniform(180, 200) if random.random() > 0.5 else random.uniform(240, 260)
            fault_code = 2
            fault_desc = "电压异常"
        elif fault_type == 'extreme':
            # 极端故障
            temperature = random.uniform(90, 100)
            voltage = random.uniform(160, 180)
            fault_code = 3
            fault_desc = "极端故障"
        else:
            # 随机故障
            temperature = random.uniform(80, 90)
            voltage = random.uniform(200, 240)
            fault_code = random.choice([1, 2, 3, 4])
            fault_desc = "随机故障"

        # 生成其他数据
        start_count = random.randint(50, 100)  # 异常高的启动次数
        stop_count = start_count + random.randint(-5, 5)
        run_time = start_count * random.randint(60, 180)  # 异常长的运行时间

        # 创建故障数据记录
        fault_data = ElevatorData.objects.create(
            elevator=elevator,
            timestamp=datetime.now(),
            start_count=start_count,
            stop_count=stop_count,
            run_time=run_time,
            temperature=temperature,
            voltage=voltage,
            fault_code=fault_code
        )

        # 更新电梯状态
        if fault_code in [1, 2, 3]:
            elevator.status = 'fault'
        else:
            elevator.status = 'warning'
        elevator.save()

        return JsonResponse({
            'status': 'success',
            'message': f'成功模拟 {fault_desc}',
            'elevator_id': elevator_id,
            'fault_type': fault_type,
            'data': {
                'temperature': round(temperature, 1),
                'voltage': round(voltage, 1),
                'start_count': start_count,
                'stop_count': stop_count,
                'fault_code': fault_code,
                'elevator_status': elevator.status
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


def predict_fault(request, elevator_id):
    """预测指定电梯的故障概率"""
    try:
        elevator = Elevator.objects.get(elevator_id=elevator_id)

        # 获取最近30条数据
        recent_data = ElevatorData.objects.filter(
            elevator=elevator
        ).order_by('-timestamp')[:30].values_list(
            'start_count', 'stop_count', 'run_time', 'temperature', 'voltage'
        )

        # 使用简化的故障预测
        prob = simple_fault_predict(list(recent_data))

        risk_level = 'high' if prob >= 0.7 else 'medium' if prob >= 0.4 else 'low'

        return JsonResponse({
            'status': 'success',
            'elevator_id': elevator_id,
            'fault_probability': round(prob, 3),
            'risk_level': risk_level,
            'data_points': len(recent_data),
            'timestamp': datetime.now().isoformat()
        })

    except Elevator.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': f'电梯 {elevator_id} 不存在'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })


# ============ WebSocket 支持 ============

def elevator_websocket(request):
    """WebSocket连接处理（简化版本，实际项目中建议使用channels）"""
    # 这里返回一个简单的响应，实际WebSocket需要使用Django Channels
    return JsonResponse({
        'message': 'WebSocket endpoint - 需要安装 django-channels 来支持实时通信',
        'suggestion': '当前使用轮询方式获取实时数据'
    })
