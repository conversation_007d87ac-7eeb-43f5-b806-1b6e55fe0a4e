from django.contrib import admin
from models import Elevator, ElevatorData


@admin.register(Elevator)
class ElevatorAdmin(admin.ModelAdmin):
    list_display = ['elevator_id', 'elevator_no', 'location', 'status', 'installation_date', 'last_maintenance_date']
    list_filter = ['status', 'installation_date']
    search_fields = ['elevator_id', 'elevator_no', 'location']
    readonly_fields = ['elevator_id']


@admin.register(ElevatorData)
class ElevatorDataAdmin(admin.ModelAdmin):
    list_display = ['elevator', 'timestamp', 'temperature', 'voltage', 'start_count', 'stop_count', 'fault_code']
    list_filter = ['elevator', 'fault_code', 'timestamp']
    search_fields = ['elevator__elevator_id']
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('elevator')
