/* 电梯监控系统专用样式 */

/* 电梯动画效果 */
@keyframes elevatorMove {
    0% { transform: translateY(0); }
    100% { transform: translateY(-10px); }
}

.elevator-car.moving {
    animation: elevatorMove 0.5s ease-in-out infinite alternate;
}

/* 楼层指示灯效果 */
.floor-indicator.active {
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    font-weight: bold;
}

/* 状态指示器动画 */
@keyframes statusPulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes dangerPulse {
    0% {
        background-color: #dc3545;
        box-shadow: 0 0 5px #dc3545;
    }
    50% {
        background-color: #ff6b7a;
        box-shadow: 0 0 15px #dc3545;
    }
    100% {
        background-color: #dc3545;
        box-shadow: 0 0 5px #dc3545;
    }
}

@keyframes warningPulse {
    0% {
        background-color: #ffc107;
        box-shadow: 0 0 3px #ffc107;
    }
    50% {
        background-color: #ffd43b;
        box-shadow: 0 0 10px #ffc107;
    }
    100% {
        background-color: #ffc107;
        box-shadow: 0 0 3px #ffc107;
    }
}

.status-fault {
    animation: dangerPulse 1s infinite;
}

.status-warning {
    animation: warningPulse 2s infinite;
}

/* 高风险电梯卡片动画 */
.elevator-card.high-risk {
    animation: cardDangerPulse 2s infinite;
    border: 2px solid #dc3545 !important;
}

@keyframes cardDangerPulse {
    0% {
        box-shadow: 0 8px 32px rgba(220, 53, 69, 0.2);
    }
    50% {
        box-shadow: 0 12px 40px rgba(220, 53, 69, 0.4);
    }
    100% {
        box-shadow: 0 8px 32px rgba(220, 53, 69, 0.2);
    }
}

/* 故障模拟按钮样式 */
.btn-warning.dropdown-toggle {
    background: linear-gradient(45deg, #ffc107, #ff8c00);
    border: none;
    transition: all 0.3s ease;
}

.btn-warning.dropdown-toggle:hover {
    background: linear-gradient(45deg, #ff8c00, #ff6b00);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
}

/* 数据面板样式增强 */
.metric-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
}

.metric-card:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 电梯井道样式增强 */
.elevator-shaft {
    background: linear-gradient(
        to bottom,
        #f8f9fa 0%,
        #e9ecef 50%,
        #f8f9fa 100%
    );
    box-shadow: inset 0 0 10px rgba(0,0,0,0.1);
}

.elevator-shaft::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, #dee2e6, #adb5bd);
    transform: translateX(-50%);
}

/* 电梯轿厢样式增强 */
.elevator-car {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: 2px solid #004085;
    box-shadow: 
        0 4px 8px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.2);
}

.elevator-car::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    height: 2px;
    background: linear-gradient(to right, rgba(255,255,255,0.3), transparent);
    border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .elevator-grid {
        grid-template-columns: 1fr;
    }
    
    .control-panel .row {
        text-align: center;
    }
    
    .control-panel .col-md-6:last-child {
        margin-top: 15px;
    }
    
    .alert-panel {
        position: relative;
        width: 100%;
        margin-top: 20px;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .card {
        background: rgba(33, 37, 41, 0.95);
        color: #f8f9fa;
    }
    
    .elevator-shaft {
        background: linear-gradient(to bottom, #495057, #343a40);
        border-color: #6c757d;
    }
    
    .metric-card {
        background: rgba(52, 58, 64, 0.8);
        border-color: #6c757d;
    }
}

/* 打印样式 */
@media print {
    .control-panel,
    .alert-panel {
        display: none;
    }
    
    .elevator-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
