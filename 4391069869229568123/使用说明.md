# 电梯监控系统使用说明

## 系统功能
- 10层楼、4个电梯的实时监控
- 电梯运行状态可视化
- 自动演示模式
- 故障预测和预警
- 传感器数据采集和分析

## 安装依赖
```bash
pip install django numpy pandas scikit-learn tensorflow requests
```

## 启动步骤

### 1. 数据库初始化
```bash
python manage.py makemigrations
python manage.py migrate
```

### 2. 创建初始数据
```bash
python init_data.py
```

### 3. 启动服务器
```bash
python manage.py runserver
```

### 4. 访问系统
- 主页: http://127.0.0.1:8000/
- 监控页面: http://127.0.0.1:8000/monitor/

## 使用方法

### 自动演示
1. 访问监控页面
2. 点击"开始自动演示"按钮
3. 观察电梯运行动画和数据变化
4. 点击"停止演示"结束

### 功能说明
- **电梯可视化**: 显示电梯在10层楼中的位置
- **实时数据**: 温度、电压、启停次数等传感器数据
- **故障预警**: 基于规则和AI模型的故障预测
- **状态监控**: 正常/预警/故障状态指示

## 文件说明
- `models.py`: 数据模型定义
- `views.py`: 视图函数和API接口
- `collector.py`: 数据采集模拟
- `predictor.py`: 故障预测模型
- `templates/`: HTML模板文件
- `static/`: 静态资源文件
- `init_data.py`: 数据初始化脚本

## 注意事项
- 系统使用模拟传感器数据
- 故障预测基于简单规则，可扩展为机器学习模型
- 前端使用JavaScript实现电梯运行仿真
- 支持实时数据更新和可视化
