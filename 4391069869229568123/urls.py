"""
URL configuration for elevator monitoring system.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
import views

urlpatterns = [
    path('admin/', admin.site.urls),
    
    # 主页面
    path('', views.index, name='index'),
    path('monitor/', views.elevator_monitor, name='elevator_monitor'),
    
    # API 接口
    path('api/elevator/data', views.receive_elevator_data, name='receive_elevator_data'),
    path('api/elevator/<str:elevator_id>/stats', views.get_elevator_stats, name='get_elevator_stats'),
    path('api/elevator/<str:elevator_id>/predict', views.predict_fault, name='predict_fault'),
    path('api/elevators/status', views.get_all_elevators_status, name='get_all_elevators_status'),
    path('api/elevator/simulate-fault', views.simulate_fault, name='simulate_fault'),
    
    # WebSocket 相关
    path('ws/elevator/', views.elevator_websocket, name='elevator_websocket'),
]

# 开发环境下提供静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
