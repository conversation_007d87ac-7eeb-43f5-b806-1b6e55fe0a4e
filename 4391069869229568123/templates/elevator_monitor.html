{% extends 'base.html' %}

{% block title %}电梯实时监控{% endblock %}

{% block extra_css %}
<style>
    .control-panel {
        position: sticky;
        top: 20px;
        z-index: 100;
    }
    
    .elevator-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .elevator-card {
        min-height: 600px;
    }
    
    .real-time-data {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        border-radius: 5px;
        padding: 10px;
        margin: 10px 0;
    }
    
    .chart-container {
        position: relative;
        height: 200px;
        margin: 10px 0;
    }
    
    .alert-panel {
        position: fixed;
        top: 100px;
        right: 20px;
        width: 300px;
        z-index: 1000;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .direction-indicator {
        font-size: 1.2em;
        margin-left: 10px;
    }
    
    .direction-up { color: #28a745; }
    .direction-down { color: #dc3545; }
    .direction-idle { color: #6c757d; }
</style>
{% endblock %}

{% block content %}
<!-- 控制面板 -->
<div class="row">
    <div class="col-12">
        <div class="card control-panel">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4><i class="fas fa-building"></i> 电梯监控中心</h4>
                        <p class="mb-0">实时监控 {{ elevators|length }} 部电梯，{{ total_floors }} 层建筑</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <button id="startDemo" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-play"></i> 开始自动演示
                        </button>
                        <button id="stopDemo" class="btn btn-danger btn-lg me-2" disabled>
                            <i class="fas fa-stop"></i> 停止演示
                        </button>
                        <button id="refreshData" class="btn btn-info btn-lg me-2">
                            <i class="fas fa-sync"></i> 刷新数据
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-warning btn-lg dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-exclamation-triangle"></i> 故障模拟
                            </button>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header">选择电梯</h6></li>
                                <li><a class="dropdown-item" href="#" onclick="showFaultModal('E001')">电梯1号 (E001)</a></li>
                                <li><a class="dropdown-item" href="#" onclick="showFaultModal('E002')">电梯2号 (E002)</a></li>
                                <li><a class="dropdown-item" href="#" onclick="showFaultModal('E003')">电梯3号 (E003)</a></li>
                                <li><a class="dropdown-item" href="#" onclick="showFaultModal('E004')">电梯4号 (E004)</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-success" href="#" onclick="resetAllFaults()">
                                    <i class="fas fa-undo"></i> 重置所有故障
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 系统状态指示器 -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value" id="totalElevators">{{ elevators|length }}</div>
                            <div class="metric-label">总电梯数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-success" id="normalCount">0</div>
                            <div class="metric-label">正常运行</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-warning" id="warningCount">0</div>
                            <div class="metric-label">预警状态</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-danger" id="faultCount">0</div>
                            <div class="metric-label">故障状态</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 电梯监控网格 -->
<div class="elevator-grid">
    {% for elevator in elevators %}
    <div class="card elevator-card" data-elevator-id="{{ elevator.elevator_id }}">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <span class="status-indicator status-normal" id="status-{{ elevator.elevator_id }}"></span>
                {{ elevator.elevator_no }}
                <span class="direction-indicator direction-idle" id="direction-{{ elevator.elevator_id }}">
                    <i class="fas fa-minus"></i>
                </span>
            </h5>
            <small class="text-muted" id="elevator-id-{{ elevator.elevator_id }}">{{ elevator.elevator_id }}</small>
        </div>
        
        <div class="card-body">
            <!-- 电梯井道可视化 -->
            <div class="elevator-container">
                <div class="elevator-shaft" id="shaft-{{ elevator.elevator_id }}">
                    <!-- 楼层指示器 -->
                    <div class="floor-indicator" style="top: 3px;">10F</div>
                    <div class="floor-indicator" style="top: 30px;">9F</div>
                    <div class="floor-indicator" style="top: 57px;">8F</div>
                    <div class="floor-indicator" style="top: 84px;">7F</div>
                    <div class="floor-indicator" style="top: 111px;">6F</div>
                    <div class="floor-indicator" style="top: 138px;">5F</div>
                    <div class="floor-indicator" style="top: 165px;">4F</div>
                    <div class="floor-indicator" style="top: 192px;">3F</div>
                    <div class="floor-indicator" style="top: 219px;">2F</div>
                    <div class="floor-indicator" style="top: 246px;">1F</div>
                    
                    <!-- 电梯轿厢 -->
                    <div class="elevator-car" id="car-{{ elevator.elevator_id }}" style="top: 246px;">
                        <span id="floor-display-{{ elevator.elevator_id }}">1F</span>
                    </div>
                </div>
            </div>
            
            <!-- 实时数据面板 -->
            <div class="data-panel">
                <div class="row">
                    <div class="col-6">
                        <div class="metric-card">
                            <div class="metric-value" id="temp-{{ elevator.elevator_id }}">25.0°C</div>
                            <div class="metric-label">温度</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-card">
                            <div class="metric-value" id="voltage-{{ elevator.elevator_id }}">220V</div>
                            <div class="metric-label">电压</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="metric-card">
                            <div class="metric-value" id="starts-{{ elevator.elevator_id }}">0</div>
                            <div class="metric-label">启动次数</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-card">
                            <div class="metric-value" id="stops-{{ elevator.elevator_id }}">0</div>
                            <div class="metric-label">停止次数</div>
                        </div>
                    </div>
                </div>
                
                <!-- 故障预测 -->
                <div class="mt-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <small>故障风险:</small>
                        <span class="badge bg-success" id="risk-{{ elevator.elevator_id }}">低风险</span>
                    </div>
                    <div class="progress mt-1" style="height: 6px;">
                        <div class="progress-bar bg-success" id="risk-bar-{{ elevator.elevator_id }}"
                             style="width: 10%"></div>
                    </div>
                </div>

                <!-- 单独重置按钮 -->
                <div class="mt-2 text-center">
                    <button class="btn btn-sm btn-outline-success"
                            onclick="resetSingleElevatorFault('{{ elevator.elevator_id }}')"
                            id="reset-btn-{{ elevator.elevator_id }}"
                            style="display: none;">
                        <i class="fas fa-undo"></i> 重置故障
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 警报面板 -->
<div class="alert-panel" id="alertPanel">
    <!-- 动态生成的警报信息 -->
</div>

<!-- 故障模拟模态框 -->
<div class="modal fade" id="faultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    故障模拟 - <span id="modalElevatorId"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>警告：</strong>此功能将人为制造电梯故障数据，用于测试风险预测系统。
                </div>

                <div class="mb-3">
                    <label class="form-label">故障类型：</label>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="faultType" id="overheat" value="overheat" checked>
                                <label class="form-check-label" for="overheat">
                                    <i class="fas fa-thermometer-full text-danger"></i> 电机过热 (85-95°C)
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="faultType" id="voltage" value="voltage">
                                <label class="form-check-label" for="voltage">
                                    <i class="fas fa-bolt text-warning"></i> 电压异常 (160-200V)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="faultType" id="extreme" value="extreme">
                                <label class="form-check-label" for="extreme">
                                    <i class="fas fa-skull text-danger"></i> 极端故障 (多重异常)
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="faultType" id="random" value="random">
                                <label class="form-check-label" for="random">
                                    <i class="fas fa-dice text-info"></i> 随机故障
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <small>
                        <strong>说明：</strong><br>
                        • 电机过热：温度超过85°C，触发高风险预警<br>
                        • 电压异常：电压偏离220V超过30V<br>
                        • 极端故障：同时出现多种异常情况<br>
                        • 随机故障：随机生成各种异常数据
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="simulateFault()">
                    <i class="fas fa-bomb"></i> 执行故障模拟
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class ElevatorSimulator {
    constructor() {
        this.elevators = {};
        this.isRunning = false;
        this.updateInterval = null;
        this.simulationInterval = null;
        this.totalFloors = {{ total_floors }};

        // 初始化电梯数据
        {% for elevator in elevators %}
        this.elevators['{{ elevator.elevator_id }}'] = {
            id: '{{ elevator.elevator_id }}',
            currentFloor: 1,
            targetFloor: 1,
            direction: 'idle',
            status: 'normal',
            temperature: 25.0,
            voltage: 220.0,
            startCount: 0,
            stopCount: 0,
            faultCode: 0,
            isMoving: false,
            isSimulatedFault: false,  // 是否为模拟故障
            simulatedFaultTime: null  // 模拟故障时间
        };
        {% endfor %}

        this.initializeEventListeners();
        this.updateDisplay();
    }

    initializeEventListeners() {
        document.getElementById('startDemo').addEventListener('click', () => this.startDemo());
        document.getElementById('stopDemo').addEventListener('click', () => this.stopDemo());
        document.getElementById('refreshData').addEventListener('click', () => this.refreshData());
    }

    startDemo() {
        if (this.isRunning) return;

        this.isRunning = true;
        document.getElementById('startDemo').disabled = true;
        document.getElementById('stopDemo').disabled = false;

        // 开始电梯运行仿真
        this.simulationInterval = setInterval(() => {
            this.simulateElevatorMovement();
        }, 2000);

        // 开始数据更新
        this.updateInterval = setInterval(() => {
            this.updateSensorData();
            this.sendDataToServer();
        }, 5000);

        this.showAlert('系统启动', '自动演示已开始，电梯开始运行', 'success');
    }

    stopDemo() {
        if (!this.isRunning) return;

        this.isRunning = false;
        document.getElementById('startDemo').disabled = false;
        document.getElementById('stopDemo').disabled = true;

        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }

        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }

        // 停止所有电梯
        Object.values(this.elevators).forEach(elevator => {
            elevator.direction = 'idle';
            elevator.isMoving = false;
        });

        this.updateDisplay();
        this.showAlert('系统停止', '自动演示已停止', 'warning');
    }

    simulateElevatorMovement() {
        Object.values(this.elevators).forEach(elevator => {
            if (!elevator.isMoving) {
                // 随机选择目标楼层
                const newTarget = Math.floor(Math.random() * this.totalFloors) + 1;
                if (newTarget !== elevator.currentFloor) {
                    elevator.targetFloor = newTarget;
                    elevator.direction = newTarget > elevator.currentFloor ? 'up' : 'down';
                    elevator.isMoving = true;
                    elevator.startCount++;
                }
            } else {
                // 移动电梯
                if (elevator.direction === 'up' && elevator.currentFloor < elevator.targetFloor) {
                    elevator.currentFloor++;
                } else if (elevator.direction === 'down' && elevator.currentFloor > elevator.targetFloor) {
                    elevator.currentFloor--;
                } else {
                    // 到达目标楼层
                    elevator.isMoving = false;
                    elevator.direction = 'idle';
                    elevator.stopCount++;
                }
            }
        });

        this.updateDisplay();
    }

    updateSensorData() {
        Object.values(this.elevators).forEach(elevator => {
            // 检查是否是人为模拟的故障（通过特殊标记识别）
            if (elevator.isSimulatedFault) {
                // 如果是模拟故障，保持故障状态不变，不更新传感器数据
                return;
            }

            // 只有在没有故障代码时才更新传感器数据（避免覆盖故障模拟数据）
            if (elevator.faultCode === 0) {
                // 模拟传感器数据变化
                const baseTemp = 25.0;
                const tempVariation = (Math.random() - 0.5) * 10; // ±5度变化
                elevator.temperature = Math.max(20, Math.min(90, baseTemp + tempVariation));

                const baseVoltage = 220.0;
                const voltageVariation = (Math.random() - 0.5) * 20; // ±10V变化
                elevator.voltage = Math.max(200, Math.min(240, baseVoltage + voltageVariation));

                // 检测故障条件
                if (elevator.temperature > 80) {
                    elevator.faultCode = 1; // 电机过热
                    elevator.status = 'fault';
                } else if (Math.abs(elevator.voltage - 220) > 25) {
                    elevator.faultCode = 2; // 电压异常
                    elevator.status = 'warning';
                } else if (elevator.temperature > 70 || Math.abs(elevator.voltage - 220) > 15) {
                    elevator.faultCode = 0;
                    elevator.status = 'warning';
                } else {
                    elevator.faultCode = 0;
                    elevator.status = 'normal';
                }
            }
            // 如果有故障代码但不是模拟故障，保持当前的故障状态不变
        });
    }

    updateDisplay() {
        Object.values(this.elevators).forEach(elevator => {
            const elevatorId = elevator.id;

            // 更新电梯位置
            const carElement = document.getElementById(`car-${elevatorId}`);
            const floorDisplay = document.getElementById(`floor-display-${elevatorId}`);
            if (carElement && floorDisplay) {
                const topPosition = (this.totalFloors - elevator.currentFloor) * 27 + 3;
                carElement.style.top = `${topPosition}px`;
                floorDisplay.textContent = `${elevator.currentFloor}F`;
            }

            // 更新方向指示器
            const directionElement = document.getElementById(`direction-${elevatorId}`);
            if (directionElement) {
                directionElement.className = `direction-indicator direction-${elevator.direction}`;
                const icon = elevator.direction === 'up' ? 'fa-arrow-up' :
                           elevator.direction === 'down' ? 'fa-arrow-down' : 'fa-minus';
                directionElement.innerHTML = `<i class="fas ${icon}"></i>`;
            }

            // 更新状态指示器
            const statusElement = document.getElementById(`status-${elevatorId}`);
            if (statusElement) {
                statusElement.className = `status-indicator status-${elevator.status}`;
            }

            // 更新传感器数据
            this.updateSensorDisplay(elevatorId, elevator);

            // 更新故障风险
            this.updateRiskDisplay(elevatorId, elevator);
        });

        // 更新总体统计
        this.updateOverallStats();
    }

    updateSensorDisplay(elevatorId, elevator) {
        const tempElement = document.getElementById(`temp-${elevatorId}`);
        const voltageElement = document.getElementById(`voltage-${elevatorId}`);
        const startsElement = document.getElementById(`starts-${elevatorId}`);
        const stopsElement = document.getElementById(`stops-${elevatorId}`);

        if (tempElement) tempElement.textContent = `${elevator.temperature.toFixed(1)}°C`;
        if (voltageElement) voltageElement.textContent = `${elevator.voltage.toFixed(1)}V`;
        if (startsElement) startsElement.textContent = elevator.startCount;
        if (stopsElement) stopsElement.textContent = elevator.stopCount;

        // 温度颜色变化
        if (tempElement) {
            if (elevator.temperature > 80) {
                tempElement.style.color = '#dc3545';
            } else if (elevator.temperature > 70) {
                tempElement.style.color = '#ffc107';
            } else {
                tempElement.style.color = '#007bff';
            }
        }

        // 电压颜色变化
        if (voltageElement) {
            if (Math.abs(elevator.voltage - 220) > 25) {
                voltageElement.style.color = '#dc3545';
            } else if (Math.abs(elevator.voltage - 220) > 15) {
                voltageElement.style.color = '#ffc107';
            } else {
                voltageElement.style.color = '#007bff';
            }
        }
    }

    updateRiskDisplay(elevatorId, elevator) {
        const riskElement = document.getElementById(`risk-${elevatorId}`);
        const riskBarElement = document.getElementById(`risk-bar-${elevatorId}`);
        const elevatorCard = document.querySelector(`[data-elevator-id="${elevatorId}"]`);

        // 计算风险等级
        let riskLevel = 'low';
        let riskPercentage = 10;
        let riskColor = 'success';

        // 更详细的风险评估
        if (elevator.faultCode > 0) {
            if (elevator.faultCode === 3 || (elevator.temperature > 90 && elevator.voltage < 180)) {
                riskLevel = 'critical';
                riskPercentage = 95;
                riskColor = 'danger';
            } else {
                riskLevel = 'high';
                riskPercentage = 80;
                riskColor = 'danger';
            }
        } else if (elevator.temperature > 80 || Math.abs(elevator.voltage - 220) > 25) {
            riskLevel = 'high';
            riskPercentage = 75;
            riskColor = 'danger';
        } else if (elevator.temperature > 70 || Math.abs(elevator.voltage - 220) > 15) {
            riskLevel = 'medium';
            riskPercentage = 45;
            riskColor = 'warning';
        } else if (elevator.temperature > 60 || Math.abs(elevator.voltage - 220) > 10) {
            riskLevel = 'low-medium';
            riskPercentage = 25;
            riskColor = 'info';
        }

        if (riskElement) {
            const riskText = {
                'low': '低风险',
                'low-medium': '轻微风险',
                'medium': '中风险',
                'high': '高风险',
                'critical': '极危险'
            };
            riskElement.textContent = riskText[riskLevel];
            riskElement.className = `badge bg-${riskColor}`;

            // 添加闪烁效果
            if (riskLevel === 'critical' || riskLevel === 'high') {
                riskElement.classList.add('alert-pulse');
            } else {
                riskElement.classList.remove('alert-pulse');
            }
        }

        if (riskBarElement) {
            riskBarElement.style.width = `${riskPercentage}%`;
            riskBarElement.className = `progress-bar bg-${riskColor}`;
        }

        // 为高风险电梯卡片添加特殊样式
        if (elevatorCard) {
            if (riskLevel === 'critical' || riskLevel === 'high') {
                elevatorCard.classList.add('high-risk');
            } else {
                elevatorCard.classList.remove('high-risk');
            }
        }

        // 显示/隐藏重置按钮
        const resetBtn = document.getElementById(`reset-btn-${elevatorId}`);
        if (resetBtn) {
            if (elevator.isSimulatedFault && (riskLevel === 'critical' || riskLevel === 'high')) {
                resetBtn.style.display = 'inline-block';
            } else {
                resetBtn.style.display = 'none';
            }
        }

        // 高风险时显示警报
        if (riskLevel === 'critical') {
            this.showAlert(
                `🚨 电梯 ${elevatorId} 极危险警报`,
                `<strong>立即停止使用！</strong><br>故障代码: ${elevator.faultCode}<br>温度: ${elevator.temperature.toFixed(1)}°C<br>电压: ${elevator.voltage.toFixed(1)}V`,
                'danger'
            );
        } else if (riskLevel === 'high') {
            this.showAlert(
                `⚠️ 电梯 ${elevatorId} 高风险警报`,
                `建议立即检查！<br>故障代码: ${elevator.faultCode}<br>温度: ${elevator.temperature.toFixed(1)}°C<br>电压: ${elevator.voltage.toFixed(1)}V`,
                'warning'
            );
        }
    }

    updateOverallStats() {
        const elevatorList = Object.values(this.elevators);
        const normalCount = elevatorList.filter(e => e.status === 'normal').length;
        const warningCount = elevatorList.filter(e => e.status === 'warning').length;
        const faultCount = elevatorList.filter(e => e.status === 'fault').length;

        document.getElementById('normalCount').textContent = normalCount;
        document.getElementById('warningCount').textContent = warningCount;
        document.getElementById('faultCount').textContent = faultCount;
    }

    async sendDataToServer() {
        for (const elevator of Object.values(this.elevators)) {
            const data = {
                timestamp: new Date().toISOString(),
                elevator_id: elevator.id,
                start_count: elevator.startCount,
                stop_count: elevator.stopCount,
                run_time: Math.floor(Math.random() * 3600), // 模拟运行时间
                temperature: elevator.temperature,
                voltage: elevator.voltage,
                fault_code: elevator.faultCode
            };

            try {
                const response = await fetch('/api/elevator/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken()
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    console.error('数据发送失败:', response.statusText);
                }
            } catch (error) {
                console.error('网络错误:', error);
            }
        }
    }

    async refreshData() {
        try {
            const response = await fetch('/api/elevators/status');
            const data = await response.json();

            if (data.status === 'success') {
                // 更新电梯状态
                data.elevators.forEach(elevatorData => {
                    if (this.elevators[elevatorData.elevator_id]) {
                        const elevator = this.elevators[elevatorData.elevator_id];

                        // 如果是模拟故障，不要被后端数据覆盖
                        if (!elevator.isSimulatedFault) {
                            // 完全使用后端数据更新
                            elevator.status = elevatorData.status;
                            elevator.temperature = elevatorData.latest_data.temperature;
                            elevator.voltage = elevatorData.latest_data.voltage;
                            elevator.startCount = elevatorData.latest_data.start_count;
                            elevator.stopCount = elevatorData.latest_data.stop_count;
                            elevator.faultCode = elevatorData.latest_data.fault_code;

                            // 如果有故障，停止自动运行
                            if (elevator.faultCode > 0) {
                                elevator.isMoving = false;
                                elevator.direction = 'idle';
                            }
                        } else {
                            // 如果是模拟故障，只更新启停次数，保持故障状态
                            elevator.startCount = elevatorData.latest_data.start_count;
                            elevator.stopCount = elevatorData.latest_data.stop_count;
                        }

                        console.log(`更新电梯 ${elevatorData.elevator_id}:`, {
                            temperature: elevator.temperature,
                            voltage: elevator.voltage,
                            faultCode: elevator.faultCode,
                            status: elevator.status,
                            isSimulatedFault: elevator.isSimulatedFault
                        });
                    }
                });

                this.updateDisplay();
                this.showAlert('数据刷新', '已获取最新数据', 'info');
            }
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showAlert('刷新失败', '无法获取最新数据', 'danger');
        }
    }

    showAlert(title, message, type = 'info') {
        const alertPanel = document.getElementById('alertPanel');
        const alertId = 'alert-' + Date.now();

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}">
                <strong>${title}</strong><br>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        alertPanel.insertAdjacentHTML('afterbegin', alertHtml);

        // 5秒后自动移除警报
        setTimeout(() => {
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                alertElement.remove();
            }
        }, 5000);
    }

    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }
}

// ============ 故障模拟功能 ============

let currentElevatorForFault = null;

function showFaultModal(elevatorId) {
    currentElevatorForFault = elevatorId;
    document.getElementById('modalElevatorId').textContent = elevatorId;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('faultModal'));
    modal.show();
}

async function simulateFault() {
    if (!currentElevatorForFault) {
        alert('请选择要模拟故障的电梯');
        return;
    }

    // 获取选中的故障类型
    const faultType = document.querySelector('input[name="faultType"]:checked').value;

    try {
        const response = await fetch('/api/elevator/simulate-fault', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': window.elevatorSimulator.getCsrfToken()
            },
            body: JSON.stringify({
                elevator_id: currentElevatorForFault,
                fault_type: faultType
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('faultModal'));
            modal.hide();

            // 显示成功消息
            window.elevatorSimulator.showAlert(
                '故障模拟成功',
                `${result.message}<br>
                电梯: ${result.elevator_id}<br>
                温度: ${result.data.temperature}°C<br>
                电压: ${result.data.voltage}V<br>
                故障代码: ${result.data.fault_code}<br>
                电梯状态: ${result.data.elevator_status}`,
                'warning'
            );

            // 如果电梯在模拟器中，立即更新其状态
            if (window.elevatorSimulator.elevators[currentElevatorForFault]) {
                const elevator = window.elevatorSimulator.elevators[currentElevatorForFault];
                elevator.temperature = result.data.temperature;
                elevator.voltage = result.data.voltage;
                elevator.startCount = result.data.start_count;
                elevator.stopCount = result.data.stop_count;
                elevator.faultCode = result.data.fault_code;
                elevator.status = result.data.elevator_status;

                // 标记为模拟故障，防止被自动更新覆盖
                elevator.isSimulatedFault = true;
                elevator.simulatedFaultTime = Date.now();

                // 如果有故障，停止电梯运行
                if (elevator.faultCode > 0) {
                    elevator.isMoving = false;
                    elevator.direction = 'idle';
                }

                console.log(`故障模拟成功 - 电梯 ${currentElevatorForFault}:`, {
                    temperature: elevator.temperature,
                    voltage: elevator.voltage,
                    faultCode: elevator.faultCode,
                    status: elevator.status,
                    isSimulatedFault: true
                });

                // 立即更新显示
                window.elevatorSimulator.updateDisplay();
            }

            // 然后刷新后端数据
            setTimeout(() => {
                window.elevatorSimulator.refreshData();
            }, 500);

        } else {
            window.elevatorSimulator.showAlert(
                '故障模拟失败',
                result.message,
                'danger'
            );
        }

    } catch (error) {
        console.error('故障模拟请求失败:', error);
        window.elevatorSimulator.showAlert(
            '网络错误',
            '无法连接到服务器，请检查网络连接',
            'danger'
        );
    }
}

function resetAllFaults() {
    if (!confirm('确定要重置所有电梯的故障状态吗？')) {
        return;
    }

    // 重置所有电梯的故障状态
    Object.values(window.elevatorSimulator.elevators).forEach(elevator => {
        elevator.faultCode = 0;
        elevator.status = 'normal';
        elevator.temperature = 25.0;
        elevator.voltage = 220.0;

        // 清除模拟故障标记
        elevator.isSimulatedFault = false;
        elevator.simulatedFaultTime = null;

        // 恢复电梯运行
        elevator.isMoving = false;
        elevator.direction = 'idle';
    });

    // 更新显示
    window.elevatorSimulator.updateDisplay();

    // 显示成功消息
    window.elevatorSimulator.showAlert(
        '故障重置成功',
        '所有电梯已恢复正常状态，可以重新开始运行',
        'success'
    );
}

function resetSingleElevatorFault(elevatorId) {
    if (!window.elevatorSimulator.elevators[elevatorId]) {
        return;
    }

    const elevator = window.elevatorSimulator.elevators[elevatorId];

    // 重置单个电梯的故障状态
    elevator.faultCode = 0;
    elevator.status = 'normal';
    elevator.temperature = 25.0;
    elevator.voltage = 220.0;
    elevator.isSimulatedFault = false;
    elevator.simulatedFaultTime = null;
    elevator.isMoving = false;
    elevator.direction = 'idle';

    // 隐藏重置按钮
    const resetBtn = document.getElementById(`reset-btn-${elevatorId}`);
    if (resetBtn) {
        resetBtn.style.display = 'none';
    }

    // 更新显示
    window.elevatorSimulator.updateDisplay();

    // 显示成功消息
    window.elevatorSimulator.showAlert(
        `电梯 ${elevatorId} 故障重置`,
        '该电梯已恢复正常状态',
        'success'
    );
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.elevatorSimulator = new ElevatorSimulator();
});
</script>
{% endblock %}
