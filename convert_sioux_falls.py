#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SiouxFalls数据转换脚本
将TNTP格式的SiouxFalls数据转换为Excel格式，兼容现有的网络分析代码
"""

import pandas as pd
import numpy as np

def convert_sioux_falls_to_excel():
    """将SiouxFalls TNTP格式数据转换为Excel格式"""
    
    print("开始转换SiouxFalls数据...")
    
    # 1. 读取节点数据
    print("读取节点数据...")
    nodes_data = []
    try:
        with open('SiouxFalls_node.tntp', 'r') as f:
            lines = f.readlines()
            for line in lines[1:]:  # 跳过标题行
                if line.strip() and not line.startswith('~'):
                    parts = line.strip().split('\t')
                    if len(parts) >= 3:
                        node_id = int(parts[0])
                        x = float(parts[1])
                        y = float(parts[2].replace(';', ''))
                        nodes_data.append({'Node': node_id, 'X': x, 'Y': y})
        print(f"成功读取 {len(nodes_data)} 个节点")
    except FileNotFoundError:
        print("错误: 找不到 SiouxFalls_node.tntp 文件")
        return None
    
    # 2. 读取网络数据
    print("读取网络数据...")
    net_data = []
    try:
        with open('SiouxFalls_net.tntp', 'r') as f:
            lines = f.readlines()
            start_reading = False
            for line in lines:
                if '<END OF METADATA>' in line:
                    start_reading = True
                    continue
                if start_reading and line.strip() and not line.startswith('~'):
                    parts = line.strip().split('\t')
                    if len(parts) >= 10:
                        init_node = int(parts[0])
                        term_node = int(parts[1])
                        capacity = float(parts[2])
                        length = float(parts[3])
                        free_flow_time = float(parts[4])
                        b = float(parts[5])
                        power = float(parts[6])
                        speed = float(parts[7])
                        toll = float(parts[8])
                        link_type = int(parts[9].replace(';', ''))
                        
                        net_data.append({
                            'init_node': init_node,
                            'term_node': term_node,
                            'capacity': capacity,
                            'length': length,
                            'free_flow_time': free_flow_time,
                            'b': b,
                            'power': power,
                            'speed': speed,
                            'toll': toll,
                            'link_type': link_type
                        })
        print(f"成功读取 {len(net_data)} 条边")
    except FileNotFoundError:
        print("错误: 找不到 SiouxFalls_net.tntp 文件")
        return None
    
    # 3. 创建link数据（基于net数据，添加流量和成本信息）
    print("生成link数据...")
    link_data = []
    np.random.seed(42)  # 设置随机种子以确保结果可重复
    for net_row in net_data:
        # 使用容量的20-40%作为流量，添加一些随机性
        volume_ratio = np.random.uniform(0.2, 0.4)
        volume = net_row['capacity'] * volume_ratio
        
        # 成本基于自由流时间，可能会因为拥堵而增加
        congestion_factor = 1 + (volume / net_row['capacity']) * 0.5
        cost = net_row['free_flow_time'] * congestion_factor
        
        link_data.append({
            'From': net_row['init_node'],
            'To': net_row['term_node'],
            'Volume': round(volume, 2),
            'Cost': round(cost, 6)
        })
    
    # 4. 读取并处理OD数据
    print("读取OD数据...")
    trip_data = []
    try:
        with open('SiouxFalls_trips.tntp', 'r') as f:
            content = f.read()
            
        # 解析Origin数据
        origins = content.split('Origin')[1:]  # 跳过第一个空元素
        
        for origin_block in origins:
            lines = origin_block.strip().split('\n')
            if not lines:
                continue
                
            origin_id = int(lines[0].strip())
            trip_data.append(['Origin', origin_id])
            
            # 收集所有目的地和流量
            od_pairs = []
            for line in lines[1:]:
                if ':' in line and line.strip():
                    # 分割每行的OD对
                    pairs = line.split(';')
                    for pair in pairs:
                        if ':' in pair:
                            try:
                                dest_str, flow_str = pair.split(':')
                                dest = int(dest_str.strip())
                                flow = float(flow_str.strip())
                                if flow > 0:  # 只保存有流量的OD对
                                    od_pairs.append(f"{dest} : {flow}")
                            except ValueError:
                                continue
            
            # 将OD对组合成一行
            if od_pairs:
                od_string = "; ".join(od_pairs)
                trip_data.append([od_string, None])
        
        print(f"成功读取 {len(origins)} 个起点的OD数据")
    except FileNotFoundError:
        print("错误: 找不到 SiouxFalls_trips.tntp 文件")
        return None
    
    # 5. 创建DataFrame
    print("创建Excel文件...")
    nodes_df = pd.DataFrame(nodes_data)
    net_df = pd.DataFrame(net_data)
    link_df = pd.DataFrame(link_data)
    trip_df = pd.DataFrame(trip_data, columns=['Unnamed: 0', 'Unnamed: 1'])
    
    # 6. 保存为Excel文件
    output_file = 'SiouxFalls网络.xlsx'
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            nodes_df.to_excel(writer, sheet_name='node', index=False)
            net_df.to_excel(writer, sheet_name='net', index=False)
            link_df.to_excel(writer, sheet_name='link', index=False)
            trip_df.to_excel(writer, sheet_name='trip', index=False)
        
        print(f"✅ 成功转换! 输出文件: {output_file}")
        print("\n数据摘要:")
        print(f"- 节点数量: {len(nodes_data)}")
        print(f"- 边数量: {len(net_data)}")
        print(f"- OD起点数量: {len(origins)}")
        print(f"- 总OD对数量: {len([x for x in trip_data if isinstance(x[0], str) and ':' in x[0]])}")
        
        return output_file
        
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        return None

if __name__ == "__main__":
    result = convert_sioux_falls_to_excel()
    if result:
        print(f"\n🎉 转换完成! 可以使用 {result} 文件进行网络分析了。")
    else:
        print("\n❌ 转换失败，请检查输入文件是否存在。")
