# 文本处理和语言处理系统

这是一个综合性的文本处理和语言处理系统，展示了从基础数据结构到高级NLP技术的完整实现。所有7个核心模块已合并到单个文件中，便于使用和部署。

## 🎯 项目概述

本项目实现了文本处理和信息检索领域的7个核心模块，每个模块都有完整的算法实现和详细的代码注释：

### 核心模块

1. **📰 文本收集器 (TextCollector)**
   - 从古腾堡计划网站抓取文本
   - 提取指定CSS类的段落内容
   - 智能文本清理和质量验证

2. **🔍 固定搭配查找器 (CollocationFinder)**
   - 哈希表：O(1)快速查找
   - 前缀树(Trie)：高效模式匹配
   - 排序数组：按频率排序的搭配

3. **📚 基础反向索引 (BasicInvertedIndex)**
   - 经典倒排索引实现
   - 支持AND/OR/短语搜索
   - TF-IDF分数计算和排序

4. **🔤 词法分析反向索引 (LexicalInvertedIndex)**
   - 词性标注(POS Tagging)
   - 词形还原(Lemmatization)
   - 高级语言学搜索功能

5. **🧠 词向量反向索引 (VectorInvertedIndex)**
   - 语义相似性搜索
   - 查询扩展技术
   - 混合搜索(关键词+语义)

6. **🤖 正则表达式DFA (RegexDFA)**
   - NFA到DFA转换算法
   - 支持基本正则表达式操作
   - 自动机可视化

7. **💻 编程语言解释器 (SimpleInterpreter)**
   - 完整的词法分析器
   - 递归下降语法分析器
   - 支持变量、运算、控制流

## 📁 项目结构

```
├── text_processing_system.py   # 主文件，包含所有模块和详细注释
├── requirements.txt             # Python依赖包列表
└── README.md                    # 项目说明文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行演示

```bash
python text_processing_system.py
```

程序会自动下载必要的NLTK数据包（首次运行时）：
- punkt（分词器）
- averaged_perceptron_tagger（词性标注器）
- wordnet（词形还原）
- stopwords（停用词）

## 📖 模块详细说明

### 1. 📰 文本收集器 (TextCollector)

**核心功能**：从古腾堡计划网站收集高质量文本数据

**技术实现**：
- **网页抓取**：使用requests + BeautifulSoup4解析HTML
- **精确定位**：通过CSS类选择器提取特定段落
- **文本验证**：自动检查文本质量（最少10个单词）
- **多段落支持**：智能提取多个相关段落

**数据源特点**：
- **古腾堡计划**：世界最大的免费电子书库
- **高质量内容**：经典文学作品和学术文本
- **稳定可靠**：公共领域内容，无版权限制
- **丰富多样**：涵盖各种主题和文体

**使用示例**：
```python
from text_processing_system import TextCollector

collector = TextCollector()
# 从古腾堡计划收集文本
texts = collector.collect_texts()
print(f"收集到 {len(texts)} 段文本")
```

### 2. 🔍 固定搭配查找器 (CollocationFinder)

**核心功能**：多数据结构实现的固定搭配检测

**算法实现**：

#### 🗂️ 哈希表实现
- **时间复杂度**：O(1)查找，O(n)插入
- **空间复杂度**：O(n)
- **优势**：查找速度最快
- **应用场景**：频率统计和快速查找

#### 🌳 前缀树(Trie)实现
- **时间复杂度**：O(m)查找，m为短语长度
- **空间复杂度**：O(ALPHABET_SIZE * N * M)
- **优势**：支持前缀匹配，空间效率高
- **应用场景**：模式匹配和自动补全

#### 📊 排序数组实现
- **时间复杂度**：O(log n)查找，O(n)插入
- **空间复杂度**：O(n)
- **优势**：天然有序，便于获取TopK
- **应用场景**：频率排序和统计分析

**支持的N-gram**：
- Bigrams (2-gram)：如"artificial intelligence"
- Trigrams (3-gram)：如"climate change impacts"
- Fourgrams (4-gram)：如"machine learning algorithms demonstrate"

**使用示例**：
```python
from text_processing_system import CollocationFinder

finder = CollocationFinder()
finder.analyze_texts(texts)

# 哈希表查找（最快）
collocations = finder.find_collocations_hash(min_frequency=2)

# 前缀树模式匹配
patterns = ["artificial intelligence", "climate change"]
results = finder.find_collocations_trie(patterns)

# 排序数组获取TopK
top_collocations = finder.get_top_collocations_sorted(10)
```

### 3. 📚 基础反向索引 (BasicInvertedIndex)

**核心功能**：经典信息检索系统的核心数据结构

**算法原理**：
反向索引是搜索引擎的基础，建立从词项到文档的映射关系。与传统的"文档→词项"正向索引相反，反向索引支持高效的"词项→文档"查找。

**数据结构设计**：
```
词项 → 倒排列表 → [文档ID1: [位置1, 位置2, ...], 文档ID2: [位置3, 位置4, ...]]
```

**核心组件**：

#### 📄 Document类
- 文档预处理和标准化
- 词汇提取和位置记录
- 统计信息计算

#### 📋 PostingList类
- 存储词项的文档出现信息
- 记录精确位置用于短语搜索
- 计算文档频率(DF)用于TF-IDF

#### 🔍 搜索算法
- **单词搜索**：O(1)直接查找
- **AND搜索**：多个倒排列表求交集
- **OR搜索**：多个倒排列表求并集
- **短语搜索**：位置验证算法
- **TF-IDF排序**：相关性分数计算

**时间复杂度分析**：
- 索引构建：O(N×M)，N为文档数，M为平均文档长度
- 单词搜索：O(1)
- 多词AND搜索：O(k×D)，k为查询词数，D为平均文档列表长度
- 短语搜索：O(D×P)，P为短语长度

**使用示例**：
```python
from text_processing_system import BasicInvertedIndex

index = BasicInvertedIndex()
index.build_index(texts)

# 单词搜索（最基础）
docs = index.search_single_word("technology")

# 布尔AND搜索（交集）
docs = index.search_multiple_words_and(["artificial", "intelligence"])

# 布尔OR搜索（并集）
docs = index.search_multiple_words_or(["climate", "environment"])

# 精确短语搜索
docs = index.phrase_search("climate change")

# TF-IDF排序搜索（最实用）
ranked_results = index.ranked_search("artificial intelligence", top_k=5)
for doc_id, score in ranked_results:
    print(f"文档{doc_id}: 相关性分数{score:.4f}")
```

### 4. 🔤 词法分析反向索引 (LexicalInvertedIndex)

**核心功能**：融合自然语言处理的高级检索系统

**语言学基础**：

#### 📝 词性标注(POS Tagging)
将每个词标注其语法角色：
- **名词(NN/NNS)**：person, technology, systems
- **动词(VB/VBD/VBG)**：develop, created, running
- **形容词(JJ)**：artificial, intelligent, advanced
- **副词(RB)**：quickly, efficiently, automatically

#### 🔄 词形还原(Lemmatization)
将词汇还原为其基本形式：
- running → run
- technologies → technology
- better → good
- mice → mouse

**技术实现**：

#### 🧠 NLTK集成
- **分词器**：punkt tokenizer
- **词性标注器**：averaged_perceptron_tagger
- **词形还原器**：WordNet lemmatizer
- **停用词过滤**：English stopwords

#### 🔧 容错机制
当NLTK不可用时，自动切换到简化实现：
- 基于规则的简单词形还原
- 启发式词性标注
- 内置停用词列表

**索引结构**：
```
原词索引：word → PostingList
词形索引：lemma → PostingList
词性索引：pos_tag → PostingList
```

**搜索能力**：
- **语义搜索**：搜索"run"可找到"running", "ran", "runs"
- **语法搜索**：查找所有名词、动词等
- **组合搜索**：词形+词性的复合查询

**使用示例**：
```python
from text_processing_system import LexicalInvertedIndex

lexical_index = LexicalInvertedIndex()
lexical_index.build_index(texts)

# 词形还原搜索（语义级别）
docs = lexical_index.search_by_lemma("technology")  # 找到technology, technologies

# 词性搜索（语法级别）
docs = lexical_index.search_by_pos("NN")   # 所有名词
docs = lexical_index.search_by_pos("VB")   # 所有动词

# 多词形还原搜索
docs = lexical_index.search_lemma_multiple_words(
    ["develop", "create"], operation="and"
)

# 组合搜索示例
# 查找包含"经济"相关词汇或"增长"相关动词的文档
economic_docs = lexical_index.search_by_lemma("economic")
growth_docs = lexical_index.search_by_lemma("grow")
result_docs = economic_docs.union(growth_docs)
```

### 5. 🧠 词向量反向索引 (VectorInvertedIndex)

**核心功能**：基于语义理解的智能检索系统

**语义搜索原理**：
传统关键词搜索只能匹配完全相同的词汇，而语义搜索通过词向量技术理解词汇的含义，能够找到语义相关但词汇不同的内容。

**词向量技术**：

#### 🎯 简化词向量模型（默认）
- **算法**：TF-IDF + 随机向量 + 余弦相似度
- **优势**：无需额外依赖，快速部署
- **维度**：100维向量空间
- **训练**：基于文档集合的统计特征

#### ⚡ 扩展支持
- **Word2Vec**：经典词嵌入模型（需要gensim）
- **BERT**：预训练Transformer模型（需要transformers）
- **自定义**：支持加载预训练词向量

**核心算法**：

#### 📊 文档向量化
```python
文档向量 = 平均(文档中所有词的词向量)
```

#### 🔍 语义相似度计算
```python
相似度 = cosine_similarity(查询向量, 文档向量)
```

#### 🔄 查询扩展
```python
扩展查询 = 原查询 + 相似词(阈值 > 0.6)
```

**搜索类型**：

#### 🎯 语义搜索
根据语义相似度排序文档，而非关键词匹配

#### 🔍 相似词查找
发现与查询词语义相近的词汇

#### 📈 查询扩展搜索
自动添加相似词扩大搜索范围

#### 🔀 混合搜索
结合关键词匹配和语义理解的综合搜索

**使用示例**：
```python
from text_processing_system import VectorInvertedIndex

# 初始化（使用简化模型）
vector_index = VectorInvertedIndex(vector_model_type="simple")
vector_index.build_index(texts)

# 语义搜索（核心功能）
results = vector_index.semantic_search("economic development", top_k=5)
for doc_id, similarity in results:
    print(f"文档{doc_id}: 语义相似度{similarity:.4f}")

# 相似词发现
similar_words = vector_index.find_similar_words("technology", topn=5)
print("与'technology'相似的词:", similar_words)

# 查询扩展搜索
expanded_docs = vector_index.expanded_search("climate change", expansion_count=3)
print(f"扩展搜索找到{len(expanded_docs)}个文档")

# 混合搜索（推荐）
hybrid_results = vector_index.hybrid_search(
    "artificial intelligence",
    weights={"keyword": 0.4, "semantic": 0.6}
)
```

**性能特点**：
- **准确性**：能理解同义词和相关概念
- **召回率**：比关键词搜索找到更多相关文档
- **灵活性**：支持模糊查询和概念搜索
- **可扩展**：易于集成更强大的预训练模型

### 6. 🤖 正则表达式DFA (RegexDFA)

**核心功能**：正则表达式引擎的底层实现

**理论基础**：
正则表达式是形式语言理论的重要应用，本模块实现了从正则表达式到确定有限自动机(DFA)的完整转换过程，这是正则表达式引擎的核心算法。

**算法流程**：
```
正则表达式 → NFA → DFA → 字符串匹配
```

#### 🔄 NFA构建算法
使用Thompson构造法，为每个正则表达式操作构建对应的NFA片段：
- **字符匹配**：单个状态转换
- **连接**：串联NFA片段
- **选择(|)**：并联NFA片段
- **Kleene星号(*)**：循环结构
- **加号(+)**：至少一次的循环
- **问号(?)**：可选匹配

#### ⚡ NFA到DFA转换
使用子集构造法(Subset Construction)：
- **ε-闭包计算**：处理空转换
- **状态合并**：多个NFA状态合并为一个DFA状态
- **转换表构建**：确定性状态转换

**支持的正则表达式**：
- `a`：匹配字符'a'
- `ab`：连接，匹配"ab"
- `a|b`：选择，匹配'a'或'b'
- `a*`：零次或多次匹配'a'
- `a+`：一次或多次匹配'a'
- `a?`：零次或一次匹配'a'

**使用示例**：
```python
from text_processing_system import RegexDFA

# 创建复杂正则表达式的DFA
dfa = RegexDFA("(a|b)*abb")

# 字符串匹配测试
test_strings = ["abb", "aabb", "babb", "ababb", "abc"]
for s in test_strings:
    result = dfa.match(s)
    print(f"'{s}' 匹配结果: {result}")

# 获取DFA详细信息
dfa_info = dfa.get_dfa_info()
print(f"DFA状态数: {dfa_info['states_count']}")
print(f"字母表: {dfa_info['alphabet']}")
```

### 7. 💻 编程语言解释器 (SimpleInterpreter)

**核心功能**：完整的编程语言实现

**编译原理基础**：
实现了一个完整的解释器，包含编译器前端的所有核心组件：

#### 📝 词法分析器(Lexer)
将源代码转换为词法单元(Token)序列：
```python
"let x = 10" → [KEYWORD(let), IDENTIFIER(x), OPERATOR(=), NUMBER(10)]
```

#### 🌳 语法分析器(Parser)
使用递归下降分析法构建抽象语法树(AST)：
```python
"x + y * 2" → BinaryOp(x, +, BinaryOp(y, *, 2))
```

#### ⚙️ 解释器(Interpreter)
遍历AST执行程序逻辑，维护运行时环境。

**支持的语言特性**：

#### 🔢 数据类型
- **数字**：整数和浮点数
- **字符串**：支持转义字符
- **布尔值**：true/false

#### 🔧 运算符
- **算术**：`+`, `-`, `*`, `/`, `%`
- **比较**：`==`, `!=`, `<`, `>`, `<=`, `>=`
- **逻辑**：`and`, `or`, `not`
- **赋值**：`=`, `+=`, `-=`, `*=`, `/=`

#### 🎛️ 控制结构
- **条件分支**：`if...else...end`
- **循环**：`while...end`
- **变量声明**：`let variable = value`

#### 📺 输入输出
- **输出**：`print expression`
- **输入**：`read variable`

**使用示例**：
```python
from text_processing_system import SimpleInterpreter

interpreter = SimpleInterpreter()

# 复杂程序示例
program = """
# 计算斐波那契数列
let n = 10
let a = 0
let b = 1
let i = 0

print "斐波那契数列前" + n + "项:"
print a
print b

while i < n - 2
    let temp = a + b
    print temp
    let a = b
    let b = temp
    let i = i + 1
end
"""

# 执行程序
output = interpreter.interpret(program)
print("程序输出:", output)

# 获取执行统计
stats = interpreter.get_execution_stats()
print(f"执行了 {stats['statements']} 条语句")
```

**技术特点**：
- **完整性**：包含词法、语法、语义分析的完整实现
- **可扩展性**：易于添加新的语言特性
- **错误处理**：详细的语法和运行时错误报告
- **调试支持**：提供执行跟踪和变量状态查看

## 🎮 使用方法

### 🚀 快速演示
```bash
python text_processing_system.py
```
程序会自动运行所有模块的演示，展示每个功能的效果。

### 🔧 模块化使用
```python
# 导入所需模块
from text_processing_system import (
    TextCollector, CollocationFinder, BasicInvertedIndex,
    LexicalInvertedIndex, VectorInvertedIndex, RegexDFA, SimpleInterpreter
)

# 1. 收集文本数据
collector = TextCollector()
texts = collector.collect_texts(source="sample")

# 2. 分析固定搭配
finder = CollocationFinder()
finder.analyze_texts(texts)
top_collocations = finder.get_top_collocations_sorted(10)

# 3. 构建搜索索引
index = BasicInvertedIndex()
index.build_index(texts)
results = index.ranked_search("artificial intelligence", top_k=5)

# 4. 语义搜索
vector_index = VectorInvertedIndex()
vector_index.build_index(texts)
semantic_results = vector_index.semantic_search("technology development")

# 5. 正则表达式匹配
dfa = RegexDFA("(ai|artificial)*intelligence")
print(dfa.match("artificial intelligence"))  # True

# 6. 程序解释执行
interpreter = SimpleInterpreter()
program = "let x = 10\nprint x * 2"
output = interpreter.interpret(program)
```

## ⭐ 技术特点

### 🏗️ 架构设计
- **单文件部署**：所有功能集成在一个文件中
- **模块化结构**：每个类独立实现，职责清晰
- **接口统一**：相似功能提供一致的API设计

### 🔧 算法实现
- **多种数据结构**：哈希表、前缀树、排序数组对比
- **经典算法**：TF-IDF、NFA转DFA、递归下降解析
- **优化策略**：时间空间复杂度平衡

### 🛡️ 容错机制
- **依赖检测**：自动检测可选依赖库
- **降级方案**：NLTK不可用时使用简化实现
- **异常处理**：网络错误时提供备选数据

### 📚 教学价值
- **详细注释**：每个算法都有完整的中文注释
- **渐进复杂度**：从基础到高级的学习路径
- **实用案例**：真实场景的应用示例

## 📊 运行效果展示

### 📰 文本收集结果
```
成功收集了 3 篇文本
文本 1: 113 个单词 (全球经济挑战)
文本 2: 110 个单词 (人工智能发展)
文本 3: 112 个单词 (气候变化影响)
```

### 🔍 固定搭配发现
```
找到 331 个二元组合
找到 332 个三元组合
找到 329 个四元组合

高频搭配示例:
1. 'climate change': 3 次
2. 'artificial intelligence': 2 次
3. 'decision making': 2 次
```

### 📚 索引构建统计
```
基础反向索引:
- 总文档数: 3
- 词汇表大小: 247
- 平均文档长度: 112 词

词法分析索引:
- 支持词性搜索: 15种词性
- 支持词形还原: 自动处理复数、时态

词向量索引:
- 向量维度: 100
- 语义相似度阈值: 0.6
```

## 🚀 扩展方向

### 📈 功能增强
- **更多文本源**：Reuters、CNN、Guardian等
- **高级正则**：字符类`[a-z]`、量词`{n,m}`支持
- **预训练模型**：集成Word2Vec、GloVe、BERT
- **编程语言**：函数定义、数组、面向对象

### 🎨 用户体验
- **Web界面**：Flask/Django Web应用
- **可视化**：搜索结果图表、DFA状态图
- **API服务**：RESTful API接口

### ⚡ 性能优化
- **分布式**：多机器索引分片
- **缓存机制**：Redis缓存热点查询
- **并行处理**：多线程文本处理

## 🛠️ 技术栈

| 组件 | 技术 | 用途 | 状态 |
|------|------|------|------|
| **核心语言** | Python 3.8+ | 主要开发语言 | ✅ 必需 |
| **文本处理** | NLTK | 词性标注、词形还原 | 🔄 可选 |
| **数值计算** | NumPy | 向量运算、矩阵操作 | ✅ 必需 |
| **机器学习** | scikit-learn | TF-IDF、余弦相似度 | ✅ 必需 |
| **网页解析** | BeautifulSoup4 | HTML解析、文本提取 | 🔄 可选 |
| **网络请求** | Requests | HTTP请求、网页抓取 | 🔄 可选 |

## 🎓 学习价值

### 📖 算法学习
- **数据结构**：哈希表、前缀树、倒排索引
- **搜索算法**：布尔搜索、TF-IDF排序、语义搜索
- **编译原理**：词法分析、语法分析、解释执行
- **自动机理论**：NFA、DFA、正则表达式

### 💡 工程实践
- **模块设计**：高内聚低耦合的架构
- **容错处理**：优雅的降级和异常处理
- **性能优化**：时间空间复杂度权衡
- **代码质量**：详细注释、清晰命名

## 📝 总结

这个项目是一个**完整的文本处理和语言处理系统**，不仅实现了7个核心模块的完整功能，更重要的是展示了：

✨ **理论与实践的结合**：从算法原理到代码实现
🔧 **工程化的思维**：模块化、容错性、可扩展性
📚 **教学友好的设计**：详细注释、渐进复杂度
🚀 **实用性导向**：真实场景的应用价值

无论是用于**学习文本处理技术**、**研究信息检索算法**，还是作为**实际项目的基础代码**，这个系统都能提供有价值的参考和启发。

---

**开始探索**: `python text_processing_system.py` 🎯
