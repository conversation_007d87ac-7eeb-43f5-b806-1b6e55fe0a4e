#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac系统中文字体修复工具
专门解决matplotlib在Mac系统上中文显示为方框的问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os

def find_chinese_fonts():
    """查找系统中可用的中文字体"""
    print("正在扫描系统字体...")
    
    # 获取所有字体
    font_list = fm.findSystemFonts()
    font_names = []
    
    for font_path in font_list:
        try:
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            font_names.append((font_name, font_path))
        except:
            continue
    
    # Mac系统常见中文字体
    chinese_fonts = [
        'PingFang SC',
        'PingFang TC', 
        'Hiragino Sans GB',
        'Hiragino Sans',
        'STHeiti',
        'STSong',
        'STKaiti',
        'STFangsong',
        'Heiti SC',
        '<PERSON>iti TC',
        'Songti SC',
        'Songti TC',
        'Kai<PERSON> SC',
        'Kai<PERSON> TC',
        'Arial Unicode MS',
        'Apple LiGothic',
        'Apple LiSung',
        'SimHei',
        'SimSun',
        'Microsoft YaHei'
    ]
    
    # 查找可用的中文字体
    available_chinese_fonts = []
    for font_name, font_path in font_names:
        for chinese_font in chinese_fonts:
            if chinese_font.lower() in font_name.lower():
                available_chinese_fonts.append((font_name, font_path))
                break
    
    # 去重
    unique_fonts = {}
    for font_name, font_path in available_chinese_fonts:
        if font_name not in unique_fonts:
            unique_fonts[font_name] = font_path
    
    return unique_fonts

def test_font(font_name):
    """测试指定字体的中文显示效果"""
    try:
        plt.figure(figsize=(8, 6))
        plt.rcParams['font.sans-serif'] = [font_name]
        plt.rcParams['axes.unicode_minus'] = False
        
        # 测试中文文本
        test_text = '中文字体测试 - 盈利能力分析'
        plt.text(0.5, 0.5, test_text, fontsize=16, ha='center', va='center')
        plt.title(f'字体测试: {font_name}', fontsize=14)
        plt.xlim(0, 1)
        plt.ylim(0, 1)
        plt.axis('off')
        
        # 保存测试图片
        filename = f'字体测试_{font_name.replace(" ", "_")}.png'
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 字体 '{font_name}' 测试完成，保存为: {filename}")
        return True
    except Exception as e:
        print(f"✗ 字体 '{font_name}' 测试失败: {e}")
        return False

def configure_best_font():
    """配置最佳的中文字体"""
    print("=== Mac系统中文字体配置工具 ===\n")
    
    # 检测系统
    system = platform.system()
    print(f"系统: {system}")
    print(f"Python版本: {platform.python_version()}")
    
    if system != 'Darwin':
        print("警告: 此工具专为Mac系统设计")
    
    # 查找中文字体
    chinese_fonts = find_chinese_fonts()
    
    if not chinese_fonts:
        print("❌ 未找到中文字体！")
        print("建议安装中文字体或使用系统自带的字体")
        return None
    
    print(f"\n找到 {len(chinese_fonts)} 个中文字体:")
    for i, font_name in enumerate(chinese_fonts.keys(), 1):
        print(f"{i}. {font_name}")
    
    # 推荐字体优先级
    recommended_fonts = [
        'PingFang SC',
        'Hiragino Sans GB', 
        'STHeiti',
        'Arial Unicode MS',
        'Heiti SC'
    ]
    
    # 找到最佳字体
    best_font = None
    for font in recommended_fonts:
        if font in chinese_fonts:
            best_font = font
            break
    
    if not best_font:
        best_font = list(chinese_fonts.keys())[0]
    
    print(f"\n推荐使用字体: {best_font}")
    
    # 测试最佳字体
    print(f"\n正在测试字体 '{best_font}'...")
    if test_font(best_font):
        print(f"✅ 字体 '{best_font}' 测试成功！")
        
        # 配置matplotlib
        plt.rcParams['font.sans-serif'] = [best_font, 'DejaVu Sans', 'Helvetica', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建配置文件
        config_code = f'''
# Mac系统matplotlib中文字体配置
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['{best_font}', 'DejaVu Sans', 'Helvetica', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

print("中文字体配置完成: {best_font}")
'''
        
        with open('matplotlib_chinese_config.py', 'w', encoding='utf-8') as f:
            f.write(config_code)
        
        print(f"配置代码已保存到: matplotlib_chinese_config.py")
        print("在其他脚本中导入此配置: from matplotlib_chinese_config import *")
        
        return best_font
    else:
        print(f"❌ 字体 '{best_font}' 测试失败")
        return None

def create_comprehensive_test():
    """创建综合字体测试"""
    print("\n创建综合字体测试图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Mac系统中文字体显示测试', fontsize=16, fontweight='bold')
    
    test_data = {
        '盈利能力': [15.2, 8.3, 12.1, 9.7],
        '偿债能力': [85.6, 92.1, 78.9, 88.3],
        '营运能力': [13, 13, 13, 13],
        '发展能力': [8.3, 9.0, 25.9, 7.1]
    }
    
    companies = ['上海环境公司', '远达环保', '首创环保', '龙净环保']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    # 柱状图
    ax1 = axes[0, 0]
    ax1.bar(companies, test_data['盈利能力'], color=colors)
    ax1.set_title('盈利能力测试(ROE%)')
    ax1.set_ylabel('ROE(%)')
    ax1.tick_params(axis='x', rotation=45)
    
    # 折线图
    ax2 = axes[0, 1]
    ax2.plot(companies, test_data['偿债能力'], marker='o', linewidth=2, color='#e74c3c')
    ax2.set_title('偿债能力测试')
    ax2.set_ylabel('偿债能力得分')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # 饼图
    ax3 = axes[1, 0]
    ax3.pie(test_data['发展能力'], labels=companies, autopct='%1.1f%%', colors=colors)
    ax3.set_title('发展能力分布测试')
    
    # 散点图
    ax4 = axes[1, 1]
    x = test_data['盈利能力']
    y = test_data['偿债能力']
    ax4.scatter(x, y, c=colors, s=100, alpha=0.7)
    for i, company in enumerate(companies):
        ax4.annotate(company, (x[i], y[i]), xytext=(5, 5), textcoords='offset points')
    ax4.set_xlabel('盈利能力(ROE%)')
    ax4.set_ylabel('偿债能力得分')
    ax4.set_title('综合能力对比测试')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('Mac中文字体综合测试.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 综合测试图已保存: Mac中文字体综合测试.png")

if __name__ == "__main__":
    # 配置最佳字体
    best_font = configure_best_font()
    
    if best_font:
        # 创建综合测试
        create_comprehensive_test()
        
        print(f"\n🎉 字体配置完成！")
        print(f"推荐字体: {best_font}")
        print(f"请检查生成的测试图片，确认中文显示正常")
        print(f"\n在financial_analysis.py中使用以下配置:")
        print(f"plt.rcParams['font.sans-serif'] = ['{best_font}', 'DejaVu Sans']")
    else:
        print("\n❌ 字体配置失败，请手动安装中文字体")
