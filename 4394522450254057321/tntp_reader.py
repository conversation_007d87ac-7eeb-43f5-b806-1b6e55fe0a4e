#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TNTP格式数据读取器
直接读取TNTP格式的交通网络数据，无需转换
"""

import pandas as pd
import numpy as np
from collections import defaultdict

class TNTPNetworkReader:
    """TNTP格式网络数据读取器"""
    
    def __init__(self, network_name="SiouxFalls"):
        self.network_name = network_name
        self.nodes_df = None
        self.edges_df = None
        self.net_df = None
        self.od_df = None
        
    def read_tntp_data(self):
        """读取TNTP格式的所有数据"""
        print(f"读取{self.network_name}网络数据...")
        
        # 读取节点数据
        self._read_node_data()
        
        # 读取网络数据
        self._read_net_data()
        
        # 创建边数据（基于网络数据生成流量和成本）
        self._create_edge_data()
        
        # 读取OD数据
        self._read_od_data()
        
        print("✅ TNTP数据读取完成")
        
    def _read_node_data(self):
        """读取节点数据"""
        node_file = f"{self.network_name}_node.tntp"
        nodes_data = []
        
        try:
            with open(node_file, 'r') as f:
                lines = f.readlines()
                for line in lines[1:]:  # 跳过标题行
                    if line.strip() and not line.startswith('~'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 3:
                            node_id = int(parts[0])
                            x = float(parts[1])
                            y = float(parts[2].replace(';', ''))
                            nodes_data.append({'Node': node_id, 'X': x, 'Y': y})
            
            self.nodes_df = pd.DataFrame(nodes_data)
            print(f"✅ 读取节点数据: {len(nodes_data)} 个节点")
            
        except FileNotFoundError:
            print(f"⚠️ 节点文件 {node_file} 不存在")
            # 创建默认节点数据
            self._create_default_nodes()
    
    def _create_default_nodes(self):
        """创建默认节点数据（基于网络数据推断）"""
        print("🔄 基于网络数据创建节点坐标...")
        
        # 先读取网络数据获取节点列表
        net_file = f"{self.network_name}_net.tntp"
        nodes = set()
        
        try:
            with open(net_file, 'r') as f:
                lines = f.readlines()
                start_reading = False
                for line in lines:
                    if '<END OF METADATA>' in line:
                        start_reading = True
                        continue
                    if start_reading and line.strip() and not line.startswith('~'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            nodes.add(int(parts[0]))  # init_node
                            nodes.add(int(parts[1]))  # term_node
            
            # 创建网格布局的节点坐标
            nodes_list = sorted(list(nodes))
            n_nodes = len(nodes_list)
            grid_size = int(np.ceil(np.sqrt(n_nodes)))
            
            nodes_data = []
            for i, node_id in enumerate(nodes_list):
                x = (i % grid_size) * 10  # 简单的网格布局
                y = (i // grid_size) * 10
                nodes_data.append({'Node': node_id, 'X': x, 'Y': y})
            
            self.nodes_df = pd.DataFrame(nodes_data)
            print(f"✅ 创建默认节点坐标: {len(nodes_data)} 个节点")
            
        except Exception as e:
            print(f"❌ 创建默认节点失败: {e}")
    
    def _read_net_data(self):
        """读取网络数据"""
        net_file = f"{self.network_name}_net.tntp"
        net_data = []
        
        try:
            with open(net_file, 'r') as f:
                lines = f.readlines()
                start_reading = False
                for line in lines:
                    if '<END OF METADATA>' in line:
                        start_reading = True
                        continue
                    if start_reading and line.strip() and not line.startswith('~'):
                        parts = line.strip().split('\t')
                        if len(parts) >= 10:
                            init_node = int(parts[0])
                            term_node = int(parts[1])
                            capacity = float(parts[2])
                            length = float(parts[3])
                            free_flow_time = float(parts[4])
                            b = float(parts[5])
                            power = float(parts[6])
                            speed = float(parts[7])
                            toll = float(parts[8])
                            link_type = int(parts[9].replace(';', ''))
                            
                            net_data.append({
                                'init_node': init_node,
                                'term_node': term_node,
                                'capacity': capacity,
                                'length': length,
                                'free_flow_time': free_flow_time,
                                'b': b,
                                'power': power,
                                'speed': speed,
                                'toll': toll,
                                'link_type': link_type
                            })
            
            self.net_df = pd.DataFrame(net_data)
            print(f"✅ 读取网络数据: {len(net_data)} 条边")
            
        except FileNotFoundError:
            print(f"❌ 网络文件 {net_file} 不存在")
    
    def _create_edge_data(self):
        """基于网络数据创建边数据（添加流量和成本信息）"""
        if self.net_df is None:
            return
            
        edge_data = []
        np.random.seed(42)  # 确保结果可重复
        
        for _, row in self.net_df.iterrows():
            # 生成合理的流量（容量的20-50%）
            volume_ratio = np.random.uniform(0.2, 0.5)
            volume = row['capacity'] * volume_ratio
            
            # 计算实际成本（考虑拥堵）
            congestion_factor = 1 + row['b'] * (volume / row['capacity']) ** row['power']
            cost = row['free_flow_time'] * congestion_factor
            
            edge_data.append({
                'From': row['init_node'],
                'To': row['term_node'],
                'Volume': round(volume, 2),
                'Cost': round(cost, 6)
            })
        
        self.edges_df = pd.DataFrame(edge_data)
        print(f"✅ 生成边数据: {len(edge_data)} 条边")
    
    def _read_od_data(self):
        """读取OD数据"""
        od_file = f"{self.network_name}_trips.tntp"
        od_data = []
        
        try:
            with open(od_file, 'r') as f:
                content = f.read()
            
            # 解析Origin数据
            origins = content.split('Origin')[1:]  # 跳过第一个空元素
            
            for origin_block in origins:
                lines = origin_block.strip().split('\n')
                if not lines:
                    continue
                    
                origin_id = int(lines[0].strip())
                
                # 收集所有目的地和流量
                for line in lines[1:]:
                    if ':' in line and line.strip():
                        # 分割每行的OD对
                        pairs = line.split(';')
                        for pair in pairs:
                            if ':' in pair:
                                try:
                                    dest_str, flow_str = pair.split(':')
                                    dest = int(dest_str.strip())
                                    flow = float(flow_str.strip())
                                    if flow > 0:  # 只保存有流量的OD对
                                        od_data.append({
                                            'O': origin_id,
                                            'D': dest,
                                            'Ton': flow
                                        })
                                except ValueError:
                                    continue
            
            self.od_df = pd.DataFrame(od_data)
            print(f"✅ 读取OD数据: {len(od_data)} 个OD对")
            
        except FileNotFoundError:
            print(f"⚠️ OD文件 {od_file} 不存在")
            # 创建简单的OD数据
            self._create_simple_od_data()
    
    def _create_simple_od_data(self):
        """创建简单的OD数据"""
        if self.nodes_df is None:
            return
            
        od_data = []
        nodes = self.nodes_df['Node'].tolist()
        np.random.seed(42)
        
        # 为每对节点创建随机流量
        for origin in nodes:
            for dest in nodes:
                if origin != dest:
                    flow = np.random.exponential(100)  # 指数分布的流量
                    if flow > 10:  # 只保留较大的流量
                        od_data.append({
                            'O': origin,
                            'D': dest,
                            'Ton': round(flow, 2)
                        })
        
        self.od_df = pd.DataFrame(od_data)
        print(f"✅ 创建简单OD数据: {len(od_data)} 个OD对")
    
    def get_data_summary(self):
        """获取数据摘要"""
        summary = {
            "nodes": len(self.nodes_df) if self.nodes_df is not None else 0,
            "edges": len(self.net_df) if self.net_df is not None else 0,
            "od_pairs": len(self.od_df) if self.od_df is not None else 0,
            "total_flow": self.od_df['Ton'].sum() if self.od_df is not None else 0
        }
        
        print(f"\n📊 {self.network_name}网络数据摘要:")
        print(f"- 节点数量: {summary['nodes']}")
        print(f"- 边数量: {summary['edges']}")
        print(f"- OD对数量: {summary['od_pairs']}")
        print(f"- 总流量: {summary['total_flow']:.2f}")
        
        return summary

if __name__ == "__main__":
    # 测试TNTP读取器
    reader = TNTPNetworkReader("SiouxFalls")
    reader.read_tntp_data()
    reader.get_data_summary()
