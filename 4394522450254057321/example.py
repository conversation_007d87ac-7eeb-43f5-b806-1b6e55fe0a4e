# -*- coding: utf-8 -*-
"""
Created on Tue Jun 10 10:33:19 2025

@author: Stone
"""
import pandas as pd
import networkx as nx
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from collections import defaultdict
from scipy.stats import pearsonr, spearmanr
import community as community_louvain  # python-louvain库
import random
import copy
import matplotlib.animation as animation

sns.set_style("whitegrid")
# 设置全局字体为支持中文的字体（Mac系统适配）
import matplotlib as mpl
import platform

# 根据系统选择合适的中文字体
if platform.system() == 'Darwin':  # Mac系统
    mpl.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'STHeiti', 'SimHei']
elif platform.system() == 'Windows':  # Windows系统
    mpl.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
else:  # Linux系统
    mpl.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'WenQuanYi Micro Hei']

mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


class SFNetworkAnalyzer:
    def __init__(self, file_path=None, use_tntp=False, network_name="SiouxFalls"):
        self.file_path = file_path
        self.use_tntp = use_tntp
        self.network_name = network_name
        self.nodes_df = None
        self.edges_df = None
        self.net_df = None
        self.od_df = None
        self.graph = None
        self.undirected_graph = None  # 无向图用于社区检测
        
    def read_data(self):
        """读取所有数据"""
        if self.use_tntp:
            # 使用TNTP格式数据
            from tntp_reader import TNTPNetworkReader
            reader = TNTPNetworkReader(self.network_name)
            reader.read_tntp_data()

            # 获取数据
            self.nodes_df = reader.nodes_df
            self.edges_df = reader.edges_df
            self.net_df = reader.net_df
            self.od_df = reader.od_df

        else:
            # 使用Excel格式数据
            # 读取节点数据
            self.nodes_df = pd.read_excel(self.file_path, sheet_name='node')

            # 读取边数据
            self.edges_df = pd.read_excel(self.file_path, sheet_name='link')

            # 读取网络数据
            self.net_df = pd.read_excel(self.file_path, sheet_name='net')

            # 读取OD数据（需要特殊处理）
            self._read_od_data()

        # 创建图结构
        self._create_graph()
    
    def _read_od_data(self):
        """读取OD数据，处理特殊格式"""
        # 读取整个trip sheet
        od_data = pd.read_excel(self.file_path, sheet_name='trip', header=None)
        
        od_list = []
        current_origin = None
        
        for _, row in od_data.iterrows():
            # 检查是否是空行
            if pd.isna(row[0]):
                continue
                
            # 检查是否是origin行
            if 'Origin' in str(row[0]):
                current_origin = int(row[1])
                continue
                
            # 处理destination行
            if current_origin is not None:
                # 分割字符串
                parts = str(row[0]).split(';')
                for part in parts:
                    if ':' in part:
                        dest_part = part.split(':')
                        dest = int(dest_part[0].strip())
                        flow = float(dest_part[1].strip())
                        od_list.append({'O': current_origin, 'D': dest, 'Ton': flow})
        
        self.od_df = pd.DataFrame(od_list)
    
    def _create_graph(self):
        """创建有向图和无向图"""
        self.graph = nx.DiGraph()
        self.undirected_graph = nx.Graph()  # 创建无向图用于社区检测
        
        # 添加节点（带坐标属性）
        for _, row in self.nodes_df.iterrows():
            self.graph.add_node(int(row["Node"]), pos=(row["X"], row["Y"]))
            self.undirected_graph.add_node(int(row["Node"]), pos=(row["X"], row["Y"]))
        
        # 添加边（带权重和自由流时间属性）
        if not self.net_df.empty:
            # 优先使用net表的数据
            for _, row in self.net_df.iterrows():
                self.graph.add_edge(int(row["init_node"]), int(row["term_node"]), 
                                  capacity=row["capacity"],
                                  length=row["length"],
                                  free_flow_time=row["free_flow_time"],
                                  b=row["b"],
                                  power=row["power"],
                                  speed=row["speed"],
                                  toll=row["toll"],
                                  link_type=row["link_type"])
                
                # 为无向图添加边，使用容量作为权重
                self.undirected_graph.add_edge(int(row["init_node"]), int(row["term_node"]), 
                                             weight=row["capacity"])
                
        if not self.edges_df.empty:
            for _, row in self.edges_df.iterrows():
                u, v = row["From"], row["To"]
                if self.graph.has_edge(u, v):
                    # 已存在的边，仅更新link特有属性
                    self.graph[u][v].update({
                        "volume": row["Volume"],
                        "cost": row["Cost"]
                    })
                else:
                    # 新增的边（仅存在于link）
                    print(f"New edge added from link: ({u}, {v})")
                    self.graph.add_edge(u, v, 
                                      volume=row["Volume"],
                                      cost=row["Cost"])
                
                # 为无向图添加边，使用流量作为权重
                self.undirected_graph.add_edge(u, v, weight=row["Volume"])
                
    def get_data_summary(self):
        """获取数据摘要"""
        summary = {
            "nodes": len(self.graph.nodes()),
            "edges": len(self.graph.edges()),
            "od_pairs": len(self.od_df) if self.od_df is not None else 0,
            "total_flow": self.od_df['Ton'].sum() if self.od_df is not None else 0
        }
        print("\n网络数据摘要:")
        print(f"- 节点数量: {summary['nodes']}")
        print(f"- 边数量: {summary['edges']}")
        print(f"- OD对数量: {summary['od_pairs']}")
        print(f"- 总流量(Ton): {summary['total_flow']:.2f}")
        return summary
                
    def draw_network(self, figsize=(14, 12), node_size=800, edge_width_scale=0.0002):
        """绘制清晰整洁的网络图"""
        plt.figure(figsize=figsize, facecolor='white')
        ax = plt.gca()
        ax.set_facecolor('white')
        
        # 获取节点位置
        pos = nx.get_node_attributes(self.graph, 'pos')
        
        # 计算坐标范围并设置紧凑的轴范围
        x_values = [pos[node][0] for node in pos]
        y_values = [pos[node][1] for node in pos]
        x_margin = (max(x_values) - min(x_values)) * 0.1
        y_margin = (max(y_values) - min(y_values)) * 0.1
        
        # 绘制边（先画边，确保节点在上层）
        if 'capacity' in next(iter(self.graph.edges(data=True)))[2]:
            edge_widths = [self.graph[u][v]['capacity'] * edge_width_scale 
                          for u, v in self.graph.edges()]
        else:
            edge_widths = [2 * edge_width_scale for _ in self.graph.edges()]
            
        edges = nx.draw_networkx_edges(self.graph, pos, width=edge_widths, 
                                     edge_color='dodgerblue', alpha=0.7, 
                                     arrows=True, arrowstyle='-|>', 
                                     arrowsize=15, ax=ax)
        
        # 绘制节点（使用更醒目的颜色）
        nodes = nx.draw_networkx_nodes(self.graph, pos, node_size=node_size, 
                                     node_color='tomato', alpha=0.9, 
                                     edgecolors='black', linewidths=1, ax=ax)
        
        # 修改后的节点标签绘制代码
        labels = {n: str(int(n)) for n in self.graph.nodes()}  # 将节点标签转换为整数再转为字符串
        nx.draw_networkx_labels(self.graph, pos, labels=labels, 
                               font_size=24, 
                               font_family='sans-serif', 
                               font_weight='bold', ax=ax)
        
        # 对边标签进行格式化（加大字体）
        if 'capacity' in next(iter(self.graph.edges(data=True)))[2]:
            edge_labels = nx.get_edge_attributes(self.graph, 'capacity')
            formatted_edge_labels = {(u, v): "{:.0f}".format(edge_labels[(u, v)]) 
                                    for u, v in edge_labels.keys()}
            
            nx.draw_networkx_edge_labels(self.graph, pos, edge_labels=formatted_edge_labels, 
                                       font_color='darkgreen', font_size=22, 
                                       font_family='sans-serif', ax=ax,
                                       bbox=dict(facecolor='white', alpha=0.7, 
                                                edgecolor='none', boxstyle='round'))
        
        # 设置紧凑的坐标轴范围
        plt.xlim(min(x_values) - x_margin, max(x_values) + x_margin)
        plt.ylim(min(y_values) - y_margin, max(y_values) + y_margin)
        
        # 美化图形
        plt.title("Transportation Network", fontsize=16, pad=20)
        plt.axis('on')
        plt.grid(False)
        plt.tight_layout()
        plt.show()
    # %%% 社区检测    
    def detect_communities(self):
        """使用Louvain算法检测社区"""
        # 计算最佳分区
        partition = community_louvain.best_partition(self.undirected_graph)
        
        # 将分区结果添加到节点属性中
        nx.set_node_attributes(self.graph, partition, 'community')
        nx.set_node_attributes(self.undirected_graph, partition, 'community')
        
        # 计算模块度
        modularity = community_louvain.modularity(partition, self.undirected_graph)
        print(f"\n社区检测结果:")
        print(f"- 发现社区数量: {len(set(partition.values()))}")
        print(f"- 模块度: {modularity:.4f}")
        
        return partition, modularity
    
    def draw_communities(self, figsize=(14, 12), node_size=800, edge_width_scale=0.0002):
        """绘制带社区分类的网络图"""
        # 确保已经进行了社区检测
        if 'community' not in next(iter(self.graph.nodes(data=True)))[1]:
            self.detect_communities()
        
        # 获取社区分区
        partition = nx.get_node_attributes(self.graph, 'community')
        communities = set(partition.values())
        num_communities = len(communities)
        
        # 为每个社区生成一个独特的颜色
        colors = plt.cm.tab20(np.linspace(0, 1, num_communities))
        community_colors = {com: colors[i] for i, com in enumerate(communities)}
        
        plt.figure(figsize=figsize, facecolor='white')
        ax = plt.gca()
        ax.set_facecolor('white')
        
        # 获取节点位置
        pos = nx.get_node_attributes(self.graph, 'pos')
        
        # 计算坐标范围并设置紧凑的轴范围
        x_values = [pos[node][0] for node in pos]
        y_values = [pos[node][1] for node in pos]
        x_margin = (max(x_values) - min(x_values)) * 0.1
        y_margin = (max(y_values) - min(y_values)) * 0.1
        
        # 绘制边（先画边，确保节点在上层）
        if 'capacity' in next(iter(self.graph.edges(data=True)))[2]:
            edge_widths = [self.graph[u][v]['capacity'] * edge_width_scale 
                          for u, v in self.graph.edges()]
        else:
            edge_widths = [2 * edge_width_scale for _ in self.graph.edges()]
            
        edges = nx.draw_networkx_edges(self.graph, pos, width=edge_widths, 
                                     edge_color='gray', alpha=0.5, 
                                     arrows=True, arrowstyle='-|>', 
                                     arrowsize=15, ax=ax)
        
        # 按社区绘制节点 - 修复了颜色参数传递方式
        for com in communities:
            nodes = [node for node in partition if partition[node] == com]
            node_colors = [community_colors[com]] * len(nodes)  # 为每个节点创建颜色列表
            nx.draw_networkx_nodes(self.graph, pos, nodelist=nodes, 
                                 node_size=node_size, 
                                 node_color=node_colors,  # 使用列表形式传递颜色
                                 alpha=0.9, 
                                 edgecolors='black', linewidths=1, 
                                 label=f'社区 {com+1}', ax=ax)
        
        # 绘制节点标签
        labels = {n: str(int(n)) for n in self.graph.nodes()}
        nx.draw_networkx_labels(self.graph, pos, labels=labels, 
                               font_size=24, 
                               font_family='sans-serif', 
                               font_weight='bold', ax=ax)
        
        # 添加图例
        plt.legend(scatterpoints=1, frameon=True, labelspacing=1, 
                  title='社区分类', fontsize=12, 
                  bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 设置紧凑的坐标轴范围
        plt.xlim(min(x_values) - x_margin, max(x_values) + x_margin)
        plt.ylim(min(y_values) - y_margin, max(y_values) + y_margin)
        
        # 美化图形
        plt.title("基于Louvain算法的交通网络社区分类", fontsize=16, pad=20)
        plt.axis('on')
        plt.grid(False)
        plt.tight_layout()
        plt.show()
        
    # %%% 随机蓄意攻击
    def random_attack(self, attack_type='node', num_attacks=1):
        """
        随机攻击网络，移除指定数量的节点或边。
    
        参数:
            attack_type (str): 攻击目标类型，'node' 或 'edge'
            num_attacks (int): 要移除的节点或边数量
    
        返回:
            metrics: 攻击后的网络指标字典
        """
        G = copy.deepcopy(self.graph)  # 使用副本，防止修改原始图
        elements = list(G.nodes() if attack_type == 'node' else G.edges())
        
        if num_attacks > len(elements):
            print("警告：攻击数量超过网络元素总数，将移除所有元素。")
            num_attacks = len(elements)
        
        to_remove = random.sample(elements, num_attacks)
        
        for element in to_remove:
            if attack_type == 'node':
                G.remove_node(element)
            else:
                G.remove_edge(*element)
    
        metrics = self._calculate_metrics(G)
        return metrics
    
    def targeted_attack(self, attack_type='node', metric='degree', num_attacks=1):
        """
        蓄意攻击网络，移除度或介数较高的节点或边。
    
        参数:
            attack_type (str): 攻击目标类型，'node' 或 'edge'
            metric (str): 评估指标，'degree' 或 'betweenness'
            num_attacks (int): 要移除的节点或边数量
    
        返回:
            metrics: 攻击后的网络指标字典
        """
        G = copy.deepcopy(self.graph)
        if attack_type == 'node':
            if metric == 'degree':
                scores = dict(G.degree())
            elif metric == 'betweenness':
                scores = nx.betweenness_centrality(G)
            else:
                raise ValueError("未知攻击指标")
    
            sorted_nodes = sorted(scores, key=scores.get, reverse=True)
            to_remove = sorted_nodes[:num_attacks]
            for node in to_remove:
                G.remove_node(node)
        else:
            # 可扩展为边攻击（如介数）
            pass
    
        metrics = self._calculate_metrics(G)
        return metrics
    
    def _calculate_metrics(self, G):
        """
        计算网络在当前状态下的关键指标。
    
        参数:
            G (nx.Graph): 当前网络图
    
        返回:
            dict: 包含最大连通分量比例、平均聚类系数、平均最短路径等指标
        """
        try:
            if nx.is_directed(G):
                largest_comp = len(max(nx.weakly_connected_components(G), key=len))
            else:
                largest_comp = len(max(nx.connected_components(G), key=len))
            total_nodes = G.number_of_nodes()
            rel_largest = largest_comp / total_nodes if total_nodes > 0 else 0
        except:
            rel_largest = 0
    
        try:
            avg_cluster = nx.average_clustering(G.to_undirected()) if nx.is_directed(G) else nx.average_clustering(G)
        except:
            avg_cluster = 0
    
        try:
            if nx.is_strongly_connected(G):
                avg_path = nx.average_shortest_path_length(G)
            else:
                largest_cc = max(nx.weakly_connected_components(G), key=len)
                subgraph = G.subgraph(largest_cc)
                avg_path = nx.average_shortest_path_length(subgraph)
        except:
            avg_path = float('inf')
    
        return {
            'largest_component_ratio': rel_largest,
            'average_clustering': avg_cluster,
            'average_shortest_path': avg_path
        }
    def plot_attack_comparison(self, num_steps=10, attack_type='node', metric='degree', figsize=(10, 6)):
        """
        绘制随机与蓄意攻击下网络指标对比图。
    
        参数:
            num_steps (int): 攻击次数
            attack_type (str): 攻击目标类型
            metric (str): 蓄意攻击使用的指标
            figsize (tuple): 图形大小
        """
        random_results = []
        targeted_results = []
    
        for i in range(1, num_steps + 1):
            random_metrics = self.random_attack(attack_type, i)
            targeted_metrics = self.targeted_attack(attack_type, metric, i)
            random_results.append(random_metrics)
            targeted_results.append(targeted_metrics)
    
        df_random = pd.DataFrame(random_results)
        df_targeted = pd.DataFrame(targeted_results)
    
        plt.figure(figsize=figsize)
        plt.plot(df_random['largest_component_ratio'], label='随机攻击', marker='o')
        plt.plot(df_targeted['largest_component_ratio'], label='蓄意攻击', marker='x')
        plt.title('最大连通分量比率随攻击次数变化')
        plt.xlabel('攻击次数')
        plt.ylabel('最大连通分量占比')
        plt.legend()
        plt.grid(True)
        plt.show()
        
    # %%% 级联失效
    def simulate_cascade(self, initial_failures=1, capacity_factor=1.5, load_metric='betweenness'):
        """
        模拟初始失效后网络的级联失效过程。
    
        参数:
            initial_failures (int): 初始失效的节点数
            capacity_factor (float): 容量为初始负载的倍数
            load_metric (str): 负载指标，'betweenness' 或 'degree'
    
        返回:
            list: 每一步失效节点的历史记录
        """
        G = copy.deepcopy(self.graph)
        history = []
    
        # 计算负载与容量
        if load_metric == 'betweenness':
            load = nx.betweenness_centrality(G)
        elif load_metric == 'degree':
            load = dict(G.degree())
        else:
            raise ValueError("未知负载指标")
    
        capacity = {n: load[n] * capacity_factor for n in G.nodes()}
        nx.set_node_attributes(G, load, 'load')
        nx.set_node_attributes(G, capacity, 'capacity')
    
        # 初始失效
        initial_nodes = random.sample(list(G.nodes()), min(initial_failures, len(G.nodes())))
        failed_nodes = set(initial_nodes)
        G.remove_nodes_from(initial_nodes)
        history.append({'step': 0, 'failed': initial_nodes})
    
        # 级联过程
        step = 1
        while True:
            # 重新计算负载
            try:
                new_load = nx.betweenness_centrality(G)
            except:
                break
    
            # 检查过载节点
            overloaded = [n for n in G.nodes() if new_load[n] > G.nodes[n]['capacity']]
            if not overloaded:
                break
            failed_nodes.update(overloaded)
            G.remove_nodes_from(overloaded)
            history.append({'step': step, 'failed': overloaded})
            step += 1
    
        return history
    

    def visualize_cascade(self, history, figsize=(12, 8), save_path=None):
        """
        可视化级联失效过程，支持静态图和动态图保存
        
        参数:
            history (list): 级联失效的历史记录
            figsize (tuple): 图形大小
            save_path (str): 动态图保存路径(如'cascade.gif')
        """
        # 生成颜色映射
        step_colors = self._generate_step_colors(len(history))
        
        # 绘制静态图
        self._draw_static_cascade(history, step_colors, figsize)
        
        # 保存动态图
        if save_path:
            self._save_animated_cascade(history, step_colors, figsize, save_path)
    
    def _generate_step_colors(self, num_steps):
        """生成每个步骤对应的颜色"""
        # 使用渐变色区分不同步骤
        cmap = plt.get_cmap('rainbow')
        return [cmap(i/num_steps) for i in range(num_steps)]
    
    def _draw_static_cascade(self, history, step_colors, figsize):
        """绘制静态级联失效图"""
        plt.figure(figsize=figsize)
        pos = nx.get_node_attributes(self.graph, 'pos')
        
        # 绘制所有节点(灰色表示未失效)
        nx.draw_networkx_nodes(self.graph, pos, node_color='lightgray', 
                              node_size=300, alpha=0.7)
        
        # 绘制所有边
        nx.draw_networkx_edges(self.graph, pos, edge_color='gray', 
                              width=1.5, alpha=0.5)
        
        # 绘制每个步骤的失效节点
        legend_handles = []
        for step, step_data in enumerate(history):
            failed_nodes = step_data['failed']
            color = step_colors[step]
            
            # 绘制当前步骤失效的节点
            nx.draw_networkx_nodes(self.graph, pos, nodelist=failed_nodes, 
                                 node_color=[color]*len(failed_nodes), node_size=300)
            
            # 创建图例句柄
            legend_handles.append(plt.Line2D([0], [0], marker='o', color='w', 
                                           label=f'步骤 {step}',
                                           markerfacecolor=color, markersize=10))
        
        # 绘制节点标签
        nx.draw_networkx_labels(self.graph, pos, font_size=10)
        
        # 添加图例
        plt.legend(handles=legend_handles, title='失效步骤', 
                  bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.title("级联失效过程 - 静态视图")
        plt.tight_layout()
        plt.show()
    
    def _save_animated_cascade(self, history, step_colors, figsize, save_path):
        """保存动态级联失效图"""
        fig, ax = plt.subplots(figsize=figsize)
        pos = nx.get_node_attributes(self.graph, 'pos')
        
        def update(frame):
            ax.clear()
            current_step = frame
            current_history = history[:current_step+1]
            
            # 绘制所有节点(灰色表示未失效)
            nx.draw_networkx_nodes(self.graph, pos, node_color='lightgray', 
                                  node_size=300, alpha=0.7, ax=ax)
            
            # 绘制所有边
            nx.draw_networkx_edges(self.graph, pos, edge_color='gray', 
                                  width=1.5, alpha=0.5, ax=ax)
            
            # 绘制已失效的节点(按步骤着色)
            for step, step_data in enumerate(current_history):
                failed_nodes = step_data['failed']
                color = step_colors[step]
                nx.draw_networkx_nodes(self.graph, pos, nodelist=failed_nodes, 
                                     node_color=[color]*len(failed_nodes), 
                                     node_size=300, ax=ax)
            
            # 绘制节点标签
            nx.draw_networkx_labels(self.graph, pos, font_size=10, ax=ax)
            
            # 创建当前步骤的图例句柄
            current_handle = plt.Line2D([0], [0], marker='o', color='w', 
                                     label=f'步骤 {current_step}',
                                     markerfacecolor=step_colors[current_step], 
                                     markersize=10)
            
            # 添加图例
            ax.legend(handles=[current_handle], title='当前失效步骤')
            
            ax.set_title(f"级联失效过程 - 步骤 {current_step}")
        
        # 创建动画
        ani = animation.FuncAnimation(
            fig, 
            update, 
            frames=len(history), 
            interval=800, 
            repeat=False
        )
        
        # 保存动画
        ani.save(save_path, writer='pillow', fps=2, dpi=100)
        plt.close()
        print(f"动态图已保存至: {save_path}")
# %% run
def convert_sioux_falls_to_excel():
    """将SiouxFalls TNTP格式数据转换为Excel格式"""
    import pandas as pd

    # 读取节点数据
    nodes_data = []
    with open('SiouxFalls_node.tntp', 'r') as f:
        lines = f.readlines()
        for line in lines[1:]:  # 跳过标题行
            if line.strip() and not line.startswith('~'):
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    node_id = int(parts[0])
                    x = float(parts[1])
                    y = float(parts[2].replace(';', ''))
                    nodes_data.append({'Node': node_id, 'X': x, 'Y': y})

    # 读取网络数据
    net_data = []
    with open('SiouxFalls_net.tntp', 'r') as f:
        lines = f.readlines()
        start_reading = False
        for line in lines:
            if '<END OF METADATA>' in line:
                start_reading = True
                continue
            if start_reading and line.strip() and not line.startswith('~'):
                parts = line.strip().split('\t')
                if len(parts) >= 10:
                    init_node = int(parts[0])
                    term_node = int(parts[1])
                    capacity = float(parts[2])
                    length = float(parts[3])
                    free_flow_time = float(parts[4])
                    b = float(parts[5])
                    power = float(parts[6])
                    speed = float(parts[7])
                    toll = float(parts[8])
                    link_type = int(parts[9].replace(';', ''))

                    net_data.append({
                        'init_node': init_node,
                        'term_node': term_node,
                        'capacity': capacity,
                        'length': length,
                        'free_flow_time': free_flow_time,
                        'b': b,
                        'power': power,
                        'speed': speed,
                        'toll': toll,
                        'link_type': link_type
                    })

    # 创建link数据（假设volume和cost）
    link_data = []
    for net_row in net_data:
        link_data.append({
            'From': net_row['init_node'],
            'To': net_row['term_node'],
            'Volume': net_row['capacity'] * 0.3,  # 假设流量为容量的30%
            'Cost': net_row['free_flow_time']
        })

    # 创建简化的trip数据（直接使用示例数据）
    trip_data = []
    # 添加第一个Origin的数据作为示例
    trip_data.append(['Origin', 1])
    trip_data.append(['1 : 0.0; 2 : 100.0; 3 : 100.0; 4 : 500.0; 5 : 200.0; 6 : 300.0; 7 : 500.0; 8 : 800.0; 9 : 500.0; 10 : 1300.0; 11 : 500.0; 12 : 200.0; 13 : 500.0; 14 : 300.0; 15 : 500.0; 16 : 500.0; 17 : 400.0; 18 : 100.0; 19 : 300.0; 20 : 300.0; 21 : 100.0; 22 : 400.0; 23 : 300.0; 24 : 100.0', None])

    # 添加更多Origin数据
    trip_data.append(['Origin', 2])
    trip_data.append(['1 : 100.0; 2 : 0.0; 3 : 100.0; 4 : 200.0; 5 : 100.0; 6 : 400.0; 7 : 200.0; 8 : 400.0; 9 : 200.0; 10 : 600.0; 11 : 200.0; 12 : 100.0; 13 : 300.0; 14 : 100.0; 15 : 100.0; 16 : 400.0; 17 : 200.0; 18 : 0.0; 19 : 100.0; 20 : 100.0; 21 : 0.0; 22 : 100.0; 23 : 0.0; 24 : 0.0', None])

    # 创建DataFrame
    nodes_df = pd.DataFrame(nodes_data)
    net_df = pd.DataFrame(net_data)
    link_df = pd.DataFrame(link_data)
    trip_df = pd.DataFrame(trip_data, columns=['Unnamed: 0', 'Unnamed: 1'])

    # 保存为Excel文件
    with pd.ExcelWriter('SiouxFalls网络.xlsx', engine='openpyxl') as writer:
        nodes_df.to_excel(writer, sheet_name='node', index=False)
        net_df.to_excel(writer, sheet_name='net', index=False)
        link_df.to_excel(writer, sheet_name='link', index=False)
        trip_df.to_excel(writer, sheet_name='trip', index=False)

    print("SiouxFalls数据已转换为Excel格式: SiouxFalls网络.xlsx")
    return 'SiouxFalls网络.xlsx'

if __name__ == "__main__":
    print("🚀 交通网络分析系统")
    print("选择数据源:")
    print("1. 原始SF网络数据 (Excel格式)")
    print("2. SiouxFalls网络数据 (TNTP格式) - 推荐")

    # 默认使用TNTP格式的SiouxFalls数据
    use_tntp = True
    network_name = "SiouxFalls"

    if use_tntp:
        print(f"📊 使用TNTP格式数据: {network_name}")
        # 创建分析器实例 - 使用TNTP格式
        exp = SFNetworkAnalyzer(use_tntp=True, network_name=network_name)
    else:
        print("📊 使用Excel格式数据")
        # 使用Excel格式数据
        file_path = "SF网络.xls"
        exp = SFNetworkAnalyzer(file_path=file_path)

    # 读取数据
    exp.read_data()

    # 打印数据摘要
    exp.get_data_summary()

    # 绘制网络图
    exp.draw_network()

    # 进行社区检测并绘制结果
    # partition, modularity = exp.detect_communities()

    # 绘制带社区分类的网络图
    # exp.draw_communities()

    # 攻击对比
    # exp.plot_attack_comparison(num_steps=10, attack_type='node', metric='betweenness')

    # 模拟级联失效
    # cascade_history = exp.simulate_cascade(initial_failures=2, capacity_factor=1.5)

    # 可视化
    # 模拟级联失效
    '''
    cascade_history = [
        {'step': 0, 'failed': [13]},
        {'step': 1, 'failed': [14, 23]},
        {'step': 2, 'failed': [7, 10, 17, 21]},
        {'step': 3, 'failed': [1, 2, 4, 5, 6, 8, 9, 16, 18, 19, 20]}
    ]
    '''
    # exp.visualize_cascade(cascade_history, save_path='cascade.gif')