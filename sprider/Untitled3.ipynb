{"cells": [{"cell_type": "code", "execution_count": 7, "id": "d9d341d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 修复版本：将运行 728 家公司 × 68 个县，共 49504 次\n", "[公司] 21ST CENTURY CENTENNIAL INSURANCE COMPANY\n", "  [县] <PERSON><PERSON><PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ⏳ 等待激活... (31/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Alachua.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Alachua\n", "  [县] <PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Baker.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — <PERSON>\n", "  [县] Bay\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Bay.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Bay\n", "  [县] Bradford\n", "    🔄 等待新窗口 (第1/3次)\n", "    ✅ 第1秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Bradford.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Bradford\n", "  [县] <PERSON><PERSON><PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Brevard.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — <PERSON><PERSON>vard\n", "  [县] <PERSON><PERSON><PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Broward.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Broward\n", "  [县] <PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Calhoun.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — <PERSON>\n", "  [县] Charlotte\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Charlotte.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Charlotte\n", "  [县] Citrus\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Citrus.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Citrus\n", "  [县] <PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第3秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Clay.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Clay\n", "  [县] <PERSON>\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第2秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Collier.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — <PERSON>\n", "  [县] Columbia\n", "    🔄 等待新窗口 (第1/3次)\n", "    ⏳ 等待中... (1/45秒)\n", "    ✅ 第3秒检测到新窗口！\n", "    📄 页面基础加载完成\n", "    ⏳ 等待导出功能激活...\n", "    ⏳ 等待激活... (1/60秒)\n", "    ⏳ 等待激活... (11/60秒)\n", "    ⏳ 等待激活... (21/60秒)\n", "    ✅ 导出按钮已激活\n", "    🎯 方法1：直接API调用...\n", "    ✅ 方法1成功\n", "    ⏳ 等待下载完成...\n", "    ✅ 下载成功: 21ST CENTURY CENTENNIAL INSURANCE COMPANY_Columbia.csv\n", "    ✔ 完成：21ST CENTURY CENTENNIAL INSURANCE COMPANY — Columbia\n", "\n", "🎉 程序执行完成！\n"]}, {"ename": "StaleElementReferenceException", "evalue": "Message: stale element reference: stale element not found in the current frame\n  (Session info: chrome=137.0.7151.104); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\nStacktrace:\n0   chromedriver                        0x0000000105051728 chromedriver + 5891880\n1   chromedriver                        0x0000000105048e7a chromedriver + 5856890\n2   chromedriver                        0x0000000104b19400 chromedriver + 418816\n3   chromedriver                        0x0000000104b2017e chromedriver + 446846\n4   chromedriver                        0x0000000104b22b83 chromedriver + 457603\n5   chromedriver                        0x0000000104b22c33 chromedriver + 457779\n6   chromedriver                        0x0000000104b6f0ef chromedriver + 770287\n7   chromedriver                        0x0000000104b6f22d chromedriver + 770605\n8   chromedriver                        0x0000000104b606a3 chromedriver + 710307\n9   chromedriver                        0x0000000104b914e2 chromedriver + 910562\n10  chromedriver                        0x0000000104b5ecc8 chromedriver + 703688\n11  chromedriver                        0x0000000104b9169e chromedriver + 911006\n12  chromedriver                        0x0000000104bb9073 chromedriver + 1073267\n13  chromedriver                        0x0000000104b912b3 chromedriver + 910003\n14  chromedriver                        0x0000000104b5d507 chromedriver + 697607\n15  chromedriver                        0x0000000104b5e171 chromedriver + 700785\n16  chromedriver                        0x000000010500f4d0 chromedriver + 5620944\n17  chromedriver                        0x000000010501345f chromedriver + 5637215\n18  chromedriver                        0x0000000104fea8e2 chromedriver + 5470434\n19  chromedriver                        0x0000000105013dcf chromedriver + 5639631\n20  chromedriver                        0x0000000104fd90a4 chromedriver + 5398692\n21  chromedriver                        0x0000000105036688 chromedriver + 5781128\n22  chromedriver                        0x0000000105036850 chromedriver + 5781584\n23  chromedriver                        0x0000000105048a41 chromedriver + 5855809\n24  libsystem_pthread.dylib             0x00007ff80695adf1 _pthread_start + 99\n25  libsystem_pthread.dylib             0x00007ff806956857 thread_start + 15\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mStaleElementReferenceException\u001b[39m            Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 876\u001b[39m\n\u001b[32m    874\u001b[39m driver.find_element(By.ID, \u001b[33m\"\u001b[39m\u001b[33mctl00_ContentPlaceHolder1_lstCounties_MoveLeft\u001b[39m\u001b[33m\"\u001b[39m).click()\n\u001b[32m    875\u001b[39m time.sleep(\u001b[32m2\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m876\u001b[39m \u001b[43mdriver\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfind_element\u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCSS_SELECTOR\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m#ctl00_ContentPlaceHolder1_lstCounties option[value=\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mct_val\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m'\u001b[39;49m\u001b[33;43m]\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclick\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    877\u001b[39m time.sleep(\u001b[32m2\u001b[39m)\n\u001b[32m    878\u001b[39m driver.find_element(By.ID, \u001b[33m\"\u001b[39m\u001b[33mctl00_ContentPlaceHolder1_lstCounties_MoveRight\u001b[39m\u001b[33m\"\u001b[39m).click()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/赚点小银子/编程/400/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webelement.py:121\u001b[39m, in \u001b[36mWebElement.click\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    113\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mclick\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    114\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Clicks the element.\u001b[39;00m\n\u001b[32m    115\u001b[39m \n\u001b[32m    116\u001b[39m \u001b[33;03m    Example:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    119\u001b[39m \u001b[33;03m    >>> element.click()\u001b[39;00m\n\u001b[32m    120\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m121\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCommand\u001b[49m\u001b[43m.\u001b[49m\u001b[43mCLICK_ELEMENT\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/赚点小银子/编程/400/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webelement.py:574\u001b[39m, in \u001b[36mWebElement._execute\u001b[39m\u001b[34m(self, command, params)\u001b[39m\n\u001b[32m    572\u001b[39m     params = {}\n\u001b[32m    573\u001b[39m params[\u001b[33m\"\u001b[39m\u001b[33mid\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m._id\n\u001b[32m--> \u001b[39m\u001b[32m574\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_parent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/赚点小银子/编程/400/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py:447\u001b[39m, in \u001b[36mWebDriver.execute\u001b[39m\u001b[34m(self, driver_command, params)\u001b[39m\n\u001b[32m    445\u001b[39m response = \u001b[38;5;28mself\u001b[39m.command_executor.execute(driver_command, params)\n\u001b[32m    446\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response:\n\u001b[32m--> \u001b[39m\u001b[32m447\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43merror_handler\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheck_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    448\u001b[39m     response[\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[38;5;28mself\u001b[39m._unwrap_value(response.get(\u001b[33m\"\u001b[39m\u001b[33mvalue\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m))\n\u001b[32m    449\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/赚点小银子/编程/400/venv/lib/python3.13/site-packages/selenium/webdriver/remote/errorhandler.py:232\u001b[39m, in \u001b[36mErrorHandler.check_response\u001b[39m\u001b[34m(self, response)\u001b[39m\n\u001b[32m    230\u001b[39m         alert_text = value[\u001b[33m\"\u001b[39m\u001b[33malert\u001b[39m\u001b[33m\"\u001b[39m].get(\u001b[33m\"\u001b[39m\u001b[33mtext\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    231\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace, alert_text)  \u001b[38;5;66;03m# type: ignore[call-arg]  # mypy is not smart enough here\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m232\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exception_class(message, screen, stacktrace)\n", "\u001b[31mStaleElementReferenceException\u001b[39m: Message: stale element reference: stale element not found in the current frame\n  (Session info: chrome=137.0.7151.104); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\nStacktrace:\n0   chromedriver                        0x0000000105051728 chromedriver + 5891880\n1   chromedriver                        0x0000000105048e7a chromedriver + 5856890\n2   chromedriver                        0x0000000104b19400 chromedriver + 418816\n3   chromedriver                        0x0000000104b2017e chromedriver + 446846\n4   chromedriver                        0x0000000104b22b83 chromedriver + 457603\n5   chromedriver                        0x0000000104b22c33 chromedriver + 457779\n6   chromedriver                        0x0000000104b6f0ef chromedriver + 770287\n7   chromedriver                        0x0000000104b6f22d chromedriver + 770605\n8   chromedriver                        0x0000000104b606a3 chromedriver + 710307\n9   chromedriver                        0x0000000104b914e2 chromedriver + 910562\n10  chromedriver                        0x0000000104b5ecc8 chromedriver + 703688\n11  chromedriver                        0x0000000104b9169e chromedriver + 911006\n12  chromedriver                        0x0000000104bb9073 chromedriver + 1073267\n13  chromedriver                        0x0000000104b912b3 chromedriver + 910003\n14  chromedriver                        0x0000000104b5d507 chromedriver + 697607\n15  chromedriver                        0x0000000104b5e171 chromedriver + 700785\n16  chromedriver                        0x000000010500f4d0 chromedriver + 5620944\n17  chromedriver                        0x000000010501345f chromedriver + 5637215\n18  chromedriver                        0x0000000104fea8e2 chromedriver + 5470434\n19  chromedriver                        0x0000000105013dcf chromedriver + 5639631\n20  chromedriver                        0x0000000104fd90a4 chromedriver + 5398692\n21  chromedriver                        0x0000000105036688 chromedriver + 5781128\n22  chromedriver                        0x0000000105036850 chromedriver + 5781584\n23  chromedriver                        0x0000000105048a41 chromedriver + 5855809\n24  libsystem_pthread.dylib             0x00007ff80695adf1 _pthread_start + 99\n25  libsystem_pthread.dylib             0x00007ff806956857 thread_start + 15\n"]}], "source": ["# ========================================================================\n", "# 🔧 恢复单个县区选择：保险公司×县区的完整组合处理\n", "# ========================================================================\n", "\n", "# ==== 本地配置 ====\n", "CHROME_DRIVER_PATH = \"/Users/<USER>/Documents/赚点小银子/编程/400/chromedriver\"\n", "DOWNLOAD_DIR = \"/Users/<USER>/Documents/赚点小银子/编程/400/QSRNG_reports\"\n", "\n", "import time\n", "import os\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait, Select\n", "from selenium.webdriver.support import expected_conditions as EC\n", "import requests\n", "from selenium.common.exceptions import JavascriptException, TimeoutException\n", "\n", "os.makedirs(DOWNLOAD_DIR, exist_ok=True)\n", "\n", "# ==== 保险公司列表 ====\n", "\n", "\n", "# ==== 手动创建的静态列表 ====\n", "insurers = [\n", "    (\"101268\", \"21ST CENTURY CENTENNIAL INSURANCE COMPANY\"),\n", "    (\"100933\", \"21ST CENTURY NORTH AMERICA INSURANCE COMPANY\"),\n", "    (\"101109\", \"21ST CENTURY PREMIER INSURANCE COMPANY\"),\n", "    (\"105540\", \"ACCELERANT NATIONAL INSURANCE COMPANY\"),\n", "    (\"200873\", \"ACCIDENT FUND INSURANCE COMPANY OF AMERICA\"),\n", "    (\"102970\", \"ACCREDITED SURETY AND CASUALTY COMPANY, INC.\"),\n", "    (\"104790\", \"ACE AMERICAN INSURANCE COMPANY\"),\n", "    (\"101278\", \"ACE EMPLOYERS INSURANCE COMPANY\"),\n", "    (\"100760\", \"ACE FIRE UNDERWRITERS INSURANCE COMPANY\"),\n", "    (\"201865\", \"ACE INSURANCE COMPANY OF THE MIDWEST\"),\n", "    (\"100634\", \"ACE PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"101736\", \"ACSTAR INSURANCE COMPANY\"),\n", "    (\"105484\", \"ADDISON INSURANCE COMPANY\"),\n", "    (\"203679\", \"ADMIRAL INDEMNITY COMPANY\"),\n", "    (\"101266\", \"AEGIS SECURITY INSURANCE COMPANY\"),\n", "    (\"100619\", \"AFFILIATED FM INSURANCE COMPANY\"),\n", "    (\"101521\", \"AFFIRMATIVE INSURANCE COMPANY\"),\n", "    (\"101911\", \"AGCS MARINE INSURANCE COMPANY\"),\n", "    (\"102216\", \"AGRI GENERAL INSURANCE COMPANY\"),\n", "    (\"100502\", \"AIG ASSURANCE COMPANY\"),\n", "    (\"101350\", \"AIG PROPERTY CASUALTY COMPANY\"),\n", "    (\"100999\", \"AIU INSURANCE COMPANY\"),\n", "    (\"100688\", \"ALEA NORTH AMERICA INSURANCE COMPANY\"),\n", "    (\"104824\", \"ALLIANZ GLOBAL RISKS US INSURANCE COMPANY\"),\n", "    (\"201024\", \"ALLIED INSURANCE COMPANY OF AMERICA\"),\n", "    (\"102215\", \"ALLIED PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"100711\", \"ALLIED WORLD INSURANCE COMPANY\"),\n", "    (\"201363\", \"ALLIED WORLD NATIONAL ASSURANCE COMPANY\"),\n", "    (\"102607\", \"ALLIED WORLD SPECIALTY INSURANCE COMPANY\"),\n", "    (\"101301\", \"ALLMERICA FINANCIAL BENEFIT INSURANCE COMPANY\"),\n", "    (\"104726\", \"ALLSTATE FIRE AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"101926\", \"ALLSTATE INDEMNITY COMPANY\"),\n", "    (\"101641\", \"ALLSTATE INSURANCE COMPANY\"),\n", "    (\"101770\", \"ALLSTATE NORTHBROOK INDEMNITY COMPANY\"),\n", "    (\"101799\", \"ALLSTATE PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"102077\", \"ALPHA PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"201491\", \"AMERICAN AGRI-BUSINESS INSURANCE COMPANY\"),\n", "    (\"102519\", \"AMERICAN ALTERNATIVE INSURANCE CORPORATION\"),\n", "    (\"101098\", \"AMERICAN AUTOMOBILE INSURANCE COMPANY\"),\n", "    (\"102808\", \"AMERICAN BANKERS INSURANCE COMPANY OF FLORIDA\"),\n", "    (\"102620\", \"AMERICAN BUILDERS INSURANCE COMPANY\"),\n", "    (\"202614\", \"AMERICAN CAPITAL ASSURANCE CORP\"),\n", "    (\"201406\", \"AMERICAN CAPITAL ASSURANCE CORP.\"),\n", "    (\"101175\", \"AMERICAN CASUALTY COMPANY OF READING, PENNSYLVANIA\"),\n", "    (\"201598\", \"AMERICAN COASTAL INSURANCE COMPANY\"),\n", "    (\"101321\", \"AMERICAN COLONIAL INSURANCE COMPANY\"),\n", "    (\"101461\", \"AMERICAN COMMERCE INSURANCE COMPANY\"),\n", "    (\"101578\", \"AMERICAN ECONOMY INSURANCE COMPANY\"),\n", "    (\"105174\", \"AMERICAN EQUITY SPECIALTY INSURANCE COMPANY\"),\n", "    (\"200896\", \"AMERICAN FAMILY CONNECT PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"101383\", \"AMERICAN FAMILY HOME INSURANCE COMPANY\"),\n", "    (\"102793\", \"AMERICAN FIRE AND CASUALTY COMPANY\"),\n", "    (\"102962\", \"AMERICAN GENERAL PROPERTY INS CO OF FLORIDA\"),\n", "    (\"101922\", \"AMERICAN GUARANTEE AND LIABILITY INSURANCE COMPANY\"),\n", "    (\"204617\", \"AMERICAN GUARDIAN SHIELD INSURANCE COMPANY\"),\n", "    (\"201485\", \"AMERICAN HALLMARK INSURANCE COMPANY OF TEXAS\"),\n", "    (\"100990\", \"AMERICAN HOME ASSURANCE COMPANY\"),\n", "    (\"201293\", \"AMERICAN INTEGRITY INSURANCE COMPANY\"),\n", "    (\"201388\", \"AMERICAN KEYSTONE INSURANCE COMPANY\"),\n", "    (\"104316\", \"AMERICAN MERCURY INSURANCE COMPANY\"),\n", "    (\"204653\", \"AMERICAN MOBILE INSURANCE EXCHANGE\"),\n", "    (\"101384\", \"AMERICAN MODERN HOME INSURANCE COMPANY\"),\n", "    (\"201035\", \"AMERICAN MODERN INSURANCE COMPANY OF FLORIDA, INC.\"),\n", "    (\"203279\", \"AMERICAN MODERN PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"201252\", \"AMERICAN MODERN SELECT INSURANCE COMPANY\"),\n", "    (\"102279\", \"AMERICAN NATIONAL GENERAL INSURANCE COMPANY\"),\n", "    (\"102266\", \"AMERICAN NATIONAL PROPERTY &amp; CASUALTY COMPANY\"),\n", "    (\"201909\", \"AMERICAN PLATINUM PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"102719\", \"AMERICAN PROPERTY INSURANCE COMPANY\"),\n", "    (\"102112\", \"AMERICAN RELIABLE INSURANCE COMPANY\"),\n", "    (\"101988\", \"AMERICAN ROAD INSURANCE COMPANY (THE)\"),\n", "    (\"102770\", \"AMERICAN SAFETY CASUALTY INSURANCE COMPANY\"),\n", "    (\"102729\", \"AMERICAN SECURITY INSURANCE COMPANY\"),\n", "    (\"103217\", \"AMERICAN SOUTHERN HOME INSURANCE COMPANY\"),\n", "    (\"102789\", \"AMERICAN SOUTHERN INSURANCE COMPANY\"),\n", "    (\"101546\", \"AMERICAN STATES INSURANCE COMPANY\"),\n", "    (\"103656\", \"AMERICAN STRATEGIC INSURANCE CORP.\"),\n", "    (\"102113\", \"AMERICAN SUMMIT INSURANCE COMPANY\"),\n", "    (\"201040\", \"AMERICAN TRADITIONS INSURANCE COMPANY\"),\n", "    (\"101784\", \"AMERICAN ZURICH INSURANCE COMPANY\"),\n", "    (\"101997\", \"AMERISURE INSURANCE COMPANY\"),\n", "    (\"101972\", \"AMERISURE MUTUAL INSURANCE COMPANY\"),\n", "    (\"202059\", \"AMERISURE PARTNERS INSURANCE COMPANY\"),\n", "    (\"101750\", \"AMEX ASSURANCE COMPANY\"),\n", "    (\"200488\", \"AMGUARD INSURANCE COMPANY\"),\n", "    (\"100625\", \"AMICA MUTUAL INSURANCE COMPANY\"),\n", "    (\"102066\", \"ARCH INDEMNITY INSURANCE COMPANY\"),\n", "    (\"102265\", \"ARCH INSURANCE COMPANY\"),\n", "    (\"101930\", \"ARGONAUT GREAT CENTRAL INSURANCE COMPANY\"),\n", "    (\"104711\", \"ARGONAUT INSURANCE COMPANY\"),\n", "    (\"101706\", \"ARGONAUT-MIDWEST INSURANCE COMPANY\"),\n", "    (\"101885\", \"ARGUS FIRE &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"102404\", \"ARMED FORCES INSURANCE EXCHANGE\"),\n", "    (\"101003\", \"ARROWOOD INDEMNITY COMPANY\"),\n", "    (\"200813\", \"ASCOT INSURANCE COMPANY\"),\n", "    (\"200844\", \"ASI ASSURANCE CORP.\"),\n", "    (\"201044\", \"ASI HOME INSURANCE CORP.\"),\n", "    (\"201764\", \"ASI PREFERRED INSURANCE CORP.\"),\n", "    (\"204361\", \"ASI SELECT INSURANCE CORP.\"),\n", "    (\"104494\", \"ASPEN AMERICAN INSURANCE COMPANY\"),\n", "    (\"102843\", \"ASSOCIATED INDUSTRIES INSURANCE COMPANY, INC.\"),\n", "    (\"105252\", \"ASSOCIATION CASUALTY INSURANCE COMPANY\"),\n", "    (\"101032\", \"ASSURANCE COMPANY OF AMERICA\"),\n", "    (\"200951\", \"ASSURANCEAMERICA INSURANCE COMPANY\"),\n", "    (\"201139\", \"ATAIN INSURANCE COMPANY\"),\n", "    (\"100491\", \"ATHOME INSURANCE COMPANY\"),\n", "    (\"100981\", \"ATLANTIC MUTUAL INSURANCE COMPANY\"),\n", "    (\"105329\", \"ATLANTIC SPECIALTY INSURANCE COMPANY\"),\n", "    (\"201428\", \"AUSTIN MUTUAL INSURANCE COMPANY\"),\n", "    (\"201397\", \"AUTO CLUB INSURANCE COMPANY OF FLORIDA\"),\n", "    (\"103533\", \"AUTO CLUB SOUTH INSURANCE COMPANY\"),\n", "    (\"101957\", \"AUTO-OWNERS INSURANCE COMPANY\"),\n", "    (\"201552\", \"AVATAR PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"102450\", \"<PERSON>VEMCO INSURANCE COMPANY\"),\n", "    (\"100072\", \"AVENTUS INSURANCE COMPANY\"),\n", "    (\"100955\", \"AXA INSURANCE COMPANY\"),\n", "    (\"104345\", \"AXA XL INSURANCE COMPANY AMERICAS\"),\n", "    (\"102074\", \"AXIS INSURANCE COMPANY\"),\n", "    (\"100957\", \"AXIS REINSURANCE COMPANY\"),\n", "    (\"103050\", \"BANKERS INSURANCE COMPANY\"),\n", "    (\"104511\", \"BANKERS STANDARD FIRE AND MARINE COMPANY\"),\n", "    (\"102968\", \"BANKERS STANDARD INSURANCE COMPANY\"),\n", "    (\"100582\", \"BEAZLEY INSURANCE COMPANY, INC.\"),\n", "    (\"101216\", \"BEDIVERE INSURANCE COMPANY\"),\n", "    (\"102417\", \"BENCHMARK INSURANCE COMPANY\"),\n", "    (\"203657\", \"BERKLEY CASUALTY COMPANY\"),\n", "    (\"102369\", \"BERKLEY INSURANCE COMPANY\"),\n", "    (\"202451\", \"BER<PERSON>LEY NATIONAL INSURANCE COMPANY\"),\n", "    (\"102294\", \"<PERSON>ER<PERSON>LE<PERSON> REGIONAL INSURANCE COMPANY\"),\n", "    (\"102425\", \"BER<PERSON>HIRE HATHAWAY DIRECT INSURANCE COMPANY\"),\n", "    (\"103808\", \"BER<PERSON>HIR<PERSON> HATHAWAY SPECIALTY INSURANCE COMPANY\"),\n", "    (\"101646\", \"BITCO GENERAL INSURANCE CORPORATION\"),\n", "    (\"101917\", \"BITCO NATIONAL INSURANCE COMPANY\"),\n", "    (\"204940\", \"BRANCH INSURANCE EXCHANGE\"),\n", "    (\"202766\", \"BRIERFIELD INSURANCE COMPANY\"),\n", "    (\"101548\", \"BROTHERHOOD MUTUAL INSURANCE COMPANY\"),\n", "    (\"102680\", \"BUILDERS ALLIANCE INSURANCE COMPANY\"),\n", "    (\"202549\", \"BUILDERS MUTUAL INSURANCE COMPANY\"),\n", "    (\"105401\", \"CALIFORNIA CASUALTY INDEMNITY EXCHANGE\"),\n", "    (\"102638\", \"CANAL INSURANCE COMPANY\"),\n", "    (\"103454\", \"CAPACITY INSURANCE COMPANY\"),\n", "    (\"102055\", \"CAPITOL INDEMNITY CORPORATION\"),\n", "    (\"103667\", \"CAPITOL PREFERRED INSURANCE COMPANY, INC.\"),\n", "    (\"102849\", \"CAROLINA CASUALTY INSURANCE COMPANY\"),\n", "    (\"101904\", \"<PERSON>ST<PERSON> KEY INDEMNITY COMPANY\"),\n", "    (\"101849\", \"CASTLE KEY INSURANCE COMPANY\"),\n", "    (\"101285\", \"CASTLEPOINT NATIONAL INSURANCE COMPANY\"),\n", "    (\"102252\", \"CATERPILLAR INSURANCE COMPANY\"),\n", "    (\"100959\", \"CEDAR INSURANCE COMPANY\"),\n", "    (\"201317\", \"CENTAURI SPECIALTY INSURANCE COMPANY\"),\n", "    (\"101036\", \"CENTENNIAL INSURANCE COMPANY\"),\n", "    (\"100860\", \"CENTRE INSURANCE COMPANY\"),\n", "    (\"104710\", \"CENTURY-NATIONAL INSURANCE COMPANY\"),\n", "    (\"101913\", \"CHICAGO INSURANCE COMPANY\"),\n", "    (\"101167\", \"CHUBB INDEMNITY INSURANCE COMPANY\"),\n", "    (\"101165\", \"CHUBB NATIONAL INSURANCE COMPANY\"),\n", "    (\"101035\", \"CHURCH INSURANCE COMPANY\"),\n", "    (\"102048\", \"CHURCH MUTUAL INSURANCE COMPANY, S.I.\"),\n", "    (\"101042\", \"CIM INSURANCE CORPORATION\"),\n", "    (\"204389\", \"CIMARRON INSURANCE COMPANY, INC.\"),\n", "    (\"106100\", \"CITIZENS PROPERTY INSURANCE CORPORATION\"),\n", "    (\"200439\", \"CITY NATIONAL INSURANCE COMPANY\"),\n", "    (\"102439\", \"CLARENDON NATIONAL INSURANCE COMPANY\"),\n", "    (\"104523\", \"CLEAR BLUE INSURANCE COMPANY\"),\n", "    (\"102295\", \"CLEAR SPRING PROPERTY AND CASUALTY COMPANY\"),\n", "    (\"204286\", \"CM REGENT INSURANCE COMPANY\"),\n", "    (\"100941\", \"COALITION INSURANCE COMPANY\"),\n", "    (\"102474\", \"COLONIAL AMERICAN CASUALTY AND SURETY COMPANY\"),\n", "    (\"101518\", \"COLONY SPECIALTY INSURANCE COMPANY\"),\n", "    (\"104557\", \"COLORADO CASUALTY INSURANCE COMPANY\"),\n", "    (\"102367\", \"COLUMBIA INSURANCE COMPANY\"),\n", "    (\"100830\", \"COMMERCE AND INDUSTRY INSURANCE COMPANY\"),\n", "    (\"104473\", \"COMMERCIAL GUARANTY INSURANCE COMPANY\"),\n", "    (\"101791\", \"CONCERT INSURANCE COMPANY\"),\n", "    (\"205459\", \"<PERSON><PERSON><PERSON> OWNERS RECIPROCAL EXCHANGE\"),\n", "    (\"100880\", \"CONSTITUTION INSURANCE COMPANY\"),\n", "    (\"101682\", \"CONTINENTAL CASUALTY COMPANY\"),\n", "    (\"104640\", \"CONTINENTAL HERITAGE INSURANCE COMPANY\"),\n", "    (\"101427\", \"CONTINENTAL INDEMNITY COMPANY\"),\n", "    (\"100984\", \"CONTINENTAL INSURANCE COMPANY\"),\n", "    (\"104668\", \"CONTRACTORS BONDING &amp; INSURANCE COMPANY\"),\n", "    (\"105451\", \"COOPERATIVA DE SEGUROS MULTIPLES DE PUERTO RICO, INC.\"),\n", "    (\"205320\", \"COPPERPOINT CASUALTY INSURANCE COMPANY\"),\n", "    (\"205318\", \"COPPERPOINT PREMIER INSURANCE COMPANY\"),\n", "    (\"101991\", \"COREPOINTE INSURANCE COMPANY\"),\n", "    (\"105320\", \"CORNERSTONE NATIONAL INSURANCE COMPANY\"),\n", "    (\"102693\", \"COTTON STATES MUTUAL INSURANCE COMPANY\"),\n", "    (\"200916\", \"COUNTRY MUTUAL INSURANCE COMPANY\"),\n", "    (\"200919\", \"COUNTRY PREFERRED INSURANCE COMPANY\"),\n", "    (\"203071\", \"CRESTBROOK INSURANCE COMPANY\"),\n", "    (\"100000\", \"CROSSROADS INSURANCE COMPANY\"),\n", "    (\"101156\", \"CRUM &amp; FORSTER INDEMNITY COMPANY\"),\n", "    (\"102056\", \"CUMIS INSURANCE SOCIETY, INC.\"),\n", "    (\"103673\", \"CYPRESS PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"102060\", \"DAIRYLAND INSURANCE COMPANY\"),\n", "    (\"101541\", \"DEALERS ASSURANCE COMPANY\"),\n", "    (\"201790\", \"DEPOSITORS INSURANCE COMPANY\"),\n", "    (\"102430\", \"DIAMOND STATE INSURANCE COMPANY\"),\n", "    (\"100630\", \"DIGITAL ADVANTAGE INSURANCE COMPANY\"),\n", "    (\"103799\", \"DIRECT GENERAL INSURANCE COMPANY\"),\n", "    (\"200489\", \"EASTGUARD INSURANCE COMPANY\"),\n", "    (\"101651\", \"ECONOMY FIRE &amp; CASUALTY COMPANY\"),\n", "    (\"101773\", \"ECONOMY PREFERRED INSURANCE COMPANY\"),\n", "    (\"105254\", \"ECONOMY PREMIER ASSURANCE COMPANY\"),\n", "    (\"201193\", \"EDISON INSURANCE COMPANY\"),\n", "    (\"203095\", \"ELEMENTS PROPERTY INSURANCE COMPANY\"),\n", "    (\"103814\", \"EMC PROPERTY &amp; CASUALTY COMPANY\"),\n", "    (\"102383\", \"EMPIRE FIRE AND MARINE INSURANCE COMPANY\"),\n", "    (\"100539\", \"EMPLOYERS FIRE INSURANCE COMPANY\"),\n", "    (\"102037\", \"EMPLOYERS INSURANCE COMPANY OF WAUSAU\"),\n", "    (\"102173\", \"EMPLOYERS MUTUAL CASUALTY COMPANY\"),\n", "    (\"200859\", \"ENCOMPASS FLORIDIAN INDEMNITY COMPANY\"),\n", "    (\"200863\", \"ENCOMPASS FLORIDIAN INSURANCE COMPANY\"),\n", "    (\"202991\", \"ENDURANCE AMERICAN INSURANCE COMPANY\"),\n", "    (\"200732\", \"ENDURANCE ASSURANCE CORPORATION\"),\n", "    (\"104880\", \"ENERGY INSURANCE MUTUAL LIMITED\"),\n", "    (\"104310\", \"ESURANCE INSURANCE COMPANY\"),\n", "    (\"203983\", \"EVEREST DENALI INSURANCE COMPANY\"),\n", "    (\"101148\", \"EVEREST NATIONAL INSURANCE COMPANY\"),\n", "    (\"203984\", \"EVEREST PREMIER INSURANCE COMPANY\"),\n", "    (\"205097\", \"EVERETT CASH MUTUAL INSURANCE CO.\"),\n", "    (\"101702\", \"EVERGREEN NATIONAL INDEMNITY COMPANY\"),\n", "    (\"102065\", \"EVERSPAN INSURANCE COMPANY\"),\n", "    (\"100889\", \"EXECUTIVE RISK INDEMNITY INC.\"),\n", "    (\"100623\", \"FACTORY MUTUAL INSURANCE COMPANY\"),\n", "    (\"100934\", \"FAIR AMERICAN INSURANCE AND REINSURANCE COMPANY\"),\n", "    (\"104704\", \"FAIRMONT PREMIER INSURANCE COMPANY\"),\n", "    (\"104361\", \"FAIRMONT SPECIALTY INSURANCE COMPANY\"),\n", "    (\"102204\", \"FALLS LAKE NATIONAL INSURANCE COMPANY\"),\n", "    (\"203721\", \"FAMILY SECURITY INSURANCE COMPANY, INC.\"),\n", "    (\"100627\", \"FARMERS CASUALTY INSURANCE COMPANY\"),\n", "    (\"100890\", \"FARMERS GROUP PROPERTY AND CASUALTY INS. CO.\"),\n", "    (\"104800\", \"FARMERS INSURANCE EXCHANGE\"),\n", "    (\"100870\", \"FARMERS PROPERTY AND CASUALTY INSURANCE CO\"),\n", "    (\"103264\", \"FARMERS SPECIALTY INSURANCE COMPANY\"),\n", "    (\"100701\", \"FARMINGTON CASUALTY COMPANY\"),\n", "    (\"102183\", \"FARMLAND MUTUAL INSURANCE COMPANY\"),\n", "    (\"201335\", \"FCCI ADVANTAGE INSURANCE COMPANY\"),\n", "    (\"103904\", \"FCCI COMMERCIAL INSURANCE COMPANY\"),\n", "    (\"102974\", \"FCCI INSURANCE COMPANY\"),\n", "    (\"100837\", \"FEDERAL INSURANCE COMPANY\"),\n", "    (\"102100\", \"FEDERATED MUTUAL INSURANCE COMPANY\"),\n", "    (\"104006\", \"FEDERATED NATIONAL INSURANCE COMPANY\"),\n", "    (\"203858\", \"FEDERATED RESERVE INSURANCE COMPANY\"),\n", "    (\"102090\", \"FEDERATED RURAL ELECTRIC INSURANCE EXCHANGE\"),\n", "    (\"102126\", \"FEDERATED SERVICE INSURANCE COMPANY\"),\n", "    (\"103275\", \"FEDNAT INSURANCE COMPANY\"),\n", "    (\"100908\", \"FIDELITY AND DEPOSIT COMPANY OF MARYLAND\"),\n", "    (\"102208\", \"FIDELITY AND GUARANTY INSURANCE COMPANY\"),\n", "    (\"102444\", \"FIDELITY AND GUARANTY INSURANCE UNDERWRITERS, INC.\"),\n", "    (\"200725\", \"FIDELITY FIRE &amp; CASUALTY COMPANY\"),\n", "    (\"104871\", \"FIRE INSURANCE EXCHANGE\"),\n", "    (\"104716\", \"FIREMAN&#39;S FUND INSURANCE COMPANY\"),\n", "    (\"104736\", \"FIRST AMERICAN PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"103583\", \"FIRST COMMUNITY INSURANCE COMPANY\"),\n", "    (\"101734\", \"FIRST FINANCIAL INSURANCE COMPANY\"),\n", "    (\"103637\", \"FIRST FLORIDIAN AUTO AND HOME INSURANCE COMPANY\"),\n", "    (\"100599\", \"FIRST LIBERTY INSURANCE CORPORATION (THE)\"),\n", "    (\"104662\", \"FIRST NATIONAL INSURANCE COMPANY OF AMERICA\"),\n", "    (\"103665\", \"FIRST PROTECTIVE INSURANCE COMPANY\"),\n", "    (\"101785\", \"FLORIDA CASUALTY INSURANCE COMPANY\"),\n", "    (\"202021\", \"FLORIDA FAMILY HOME INSURANCE COMPANY\"),\n", "    (\"103636\", \"FLORIDA FAMILY INSURANCE COMPANY\"),\n", "    (\"103014\", \"FLORIDA FARM BUREAU CASUALTY INSURANCE COMPANY\"),\n", "    (\"103567\", \"FLORIDA FARM BUREAU GENERAL INSURANCE COMPANY\"),\n", "    (\"201068\", \"FLORIDA PENINSULA INSURANCE COMPANY\"),\n", "    (\"102377\", \"FLORIDA SPECIALTY INSURANCE COMPANY\"),\n", "    (\"101946\", \"FLORISTS&#39; INSURANCE COMPANY\"),\n", "    (\"101928\", \"FLORISTS&#39; MUTUAL INSURANCE COMPANY\"),\n", "    (\"101597\", \"FMH AG RISK INSURANCE COMPANY\"),\n", "    (\"101983\", \"FOREMOST INSURANCE COMPANY GRAND RAPIDS, MICHIGAN\"),\n", "    (\"101609\", \"FOREMOST PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"102015\", \"FOREMOST SIGNATURE INSURANCE COMPANY\"),\n", "    (\"101965\", \"FRANKENMUTH INSURANCE COMPANY\"),\n", "    (\"104509\", \"FREEDOM SPECIALTY INSURANCE COMPANY\"),\n", "    (\"200895\", \"GARRISON PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"104464\", \"GEICO GENERAL INSURANCE COMPANY\"),\n", "    (\"102038\", \"GENERAL CASUALTY COMPANY OF WISCONSIN\"),\n", "    (\"200831\", \"GENERAL FIDELITY INSURANCE COMPANY\"),\n", "    (\"104657\", \"GENERAL INSURANCE COMPANY OF AMERICA\"),\n", "    (\"100902\", \"GENERAL SECURITY NATIONAL INSURANCE COMPANY\"),\n", "    (\"100834\", \"GENERAL STAR NATIONAL INSURANCE COMPANY\"),\n", "    (\"101024\", \"GENERALI - U. S. BRANCH\"),\n", "    (\"100689\", \"GENESIS INSURANCE COMPANY\"),\n", "    (\"205399\", \"GEORGIA BUILDERS INSURANCE COMPANY\"),\n", "    (\"201010\", \"GEOVERA INSURANCE COMPANY\"),\n", "    (\"102466\", \"GLATFELTER INSURANCE COMPANY\"),\n", "    (\"102363\", \"GLENCAR INSURANCE COMPANY\"),\n", "    (\"102527\", \"GOVERNMENT EMPLOYEES INSURANCE COMPANY\"),\n", "    (\"202696\", \"GRANADA INDEMNITY COMPANY\"),\n", "    (\"103430\", \"GRANADA INSURANCE COMPANY\"),\n", "    (\"100487\", \"GRANITE STATE INSURANCE COMPANY\"),\n", "    (\"100995\", \"GRAPHIC ARTS MUTUAL INSURANCE COMPANY\"),\n", "    (\"104766\", \"GREAT AMERICAN ALLIANCE INSURANCE COMPANY\"),\n", "    (\"101064\", \"GREAT AMERICAN ASSURANCE COMPANY\"),\n", "    (\"103732\", \"GREAT AMERICAN CASUALTY INSURANCE COMPANY\"),\n", "    (\"101897\", \"GREAT AMERICAN CONTEMPORARY INSURANCE COMPANY\"),\n", "    (\"101371\", \"GREAT AMERICAN INSURANCE COMPANY\"),\n", "    (\"101013\", \"GREAT AMERICAN INSURANCE COMPANY OF NEW YORK\"),\n", "    (\"101431\", \"GREAT AMERICAN SECURITY INSURANCE COMPANY\"),\n", "    (\"101433\", \"GREAT AMERICAN SPIRIT INSURANCE COMPANY\"),\n", "    (\"102330\", \"GREAT DIVIDE INSURANCE COMPANY\"),\n", "    (\"104521\", \"GREAT MIDWEST INSURANCE COMPANY\"),\n", "    (\"102111\", \"GREAT NORTHERN INSURANCE COMPANY\"),\n", "    (\"104765\", \"GREENWICH INSURANCE COMPANY\"),\n", "    (\"100838\", \"GREENWOOD INSURANCE COMPANY\"),\n", "    (\"101793\", \"GUIDEONE AMERICA INSURANCE COMPANY\"),\n", "    (\"102217\", \"GUIDEONE ELITE INSURANCE COMPANY\"),\n", "    (\"102186\", \"GUIDEONE INSURANCE COMPANY\"),\n", "    (\"102187\", \"GUIDEONE SPECIALTY INSURANCE COMPANY\"),\n", "    (\"200888\", \"GULFSTREAM PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"100601\", \"HANOVER AMERICAN INSURANCE COMPANY (THE)\"),\n", "    (\"100991\", \"HANOVER INSURANCE COMPANY (THE)\"),\n", "    (\"101038\", \"HARCO NATIONAL INSURANCE COMPANY\"),\n", "    (\"102099\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INSURANCE COMPANY\"),\n", "    (\"100645\", \"HARTFORD ACCIDENT AND INDEMNITY COMPANY\"),\n", "    (\"100638\", \"HARTFORD CASUALTY INSURANCE COMPANY\"),\n", "    (\"100647\", \"HARTFORD FIRE INSURANCE COMPANY\"),\n", "    (\"100682\", \"HARTFORD INSURANCE COMPANY OF THE MIDWEST\"),\n", "    (\"100685\", \"HARTFORD INSURANCE COMPANY OF THE SOUTHEAST\"),\n", "    (\"100648\", \"HARTFORD STEAM BOILER INSPECTION &amp; INS. COMPANY\"),\n", "    (\"105528\", \"HARTFORD STEAM BOILER INSPECTION AND INSURANCE CO. OF CT (THE)\"),\n", "    (\"100718\", \"HARTFORD UNDERWRITERS INSURANCE COMPANY\"),\n", "    (\"201919\", \"HDI GLOBAL INSURANCE COMPANY\"),\n", "    (\"101554\", \"HDI GLOBAL SELECT INSURANCE COMPANY\"),\n", "    (\"104834\", \"HERITAGE INDEMNITY COMPANY\"),\n", "    (\"202847\", \"HERITAGE PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"201021\", \"HILLCREST INSURANCE COMPANY\"),\n", "    (\"104882\", \"HISCOX  INSURANCE COMPANY INC.\"),\n", "    (\"201559\", \"HOMEOWNERS CHOICE PROPERTY &amp; CASUALTY INSURANCE COMPANY, INC.\"),\n", "    (\"100707\", \"HOMESITE INSURANCE COMPANY\"),\n", "    (\"105200\", \"HOMESITE INSURANCE COMPANY OF FLORIDA\"),\n", "    (\"201063\", \"HOMEWISE INSURANCE COMPANY\"),\n", "    (\"201196\", \"HOMEWISE INSURANCE COMPANY, INC.\"),\n", "    (\"201380\", \"HOMEWISE PREFERRED INSURANCE COMPANY\"),\n", "    (\"102913\", \"HORACE MANN INSURANCE COMPANY\"),\n", "    (\"200898\", \"HOUSING AUTHORITY PROPERTY INSURANCE, A MUTUAL COMPANY\"),\n", "    (\"202123\", \"HOUSING ENTERPRISE INSURANCE COMPANY, INC.\"),\n", "    (\"100993\", \"HUDSON INSURANCE COMPANY\"),\n", "    (\"102757\", \"ILLINOIS INSURANCE COMPANY\"),\n", "    (\"101932\", \"ILLINOIS NATIONAL INSURANCE COMPANY\"),\n", "    (\"201328\", \"IMPERIAL FIRE AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"100894\", \"IMPERIUM INSURANCE COMPANY\"),\n", "    (\"204285\", \"INCLINE CASUALTY COMPANY\"),\n", "    (\"100686\", \"INDEMNITY INSURANCE COMPANY OF NORTH AMERICA\"),\n", "    (\"101553\", \"INDIANA INSURANCE COMPANY\"),\n", "    (\"101534\", \"INFINITY INDEMNITY INSURANCE COMPANY\"),\n", "    (\"101405\", \"INFINITY INSURANCE COMPANY\"),\n", "    (\"101182\", \"INSURANCE COMPANY OF NORTH AMERICA\"),\n", "    (\"101014\", \"INSURANCE COMPANY OF THE STATE OF PENNSY<PERSON>VANIA\"),\n", "    (\"104809\", \"INSURANCE COMPANY OF THE WEST\"),\n", "    (\"204820\", \"INSUREMAX INSURANCE COMPANY\"),\n", "    (\"102598\", \"INTEGON GENERAL INSURANCE CORPORATION\"),\n", "    (\"102587\", \"INTEGON INDEMNITY CORPORATION\"),\n", "    (\"100982\", \"INTEGON NATIONAL INSURANCE COMPANY\"),\n", "    (\"100678\", \"INTEGON PREFERRED INSURANCE COMPANY\"),\n", "    (\"205139\", \"INTREPID CASUALTY COMPANY\"),\n", "    (\"200552\", \"INTREPID INSURANCE COMPANY\"),\n", "    (\"102093\", \"IRONSHORE INDEMNITY  INC.\"),\n", "    (\"101015\", \"JEFFERSON INSURANCE COMPANY\"),\n", "    (\"102043\", \"JEWELERS MUTUAL INSURANCE COMPANY, S.I.\"),\n", "    (\"204219\", \"JOURNEY INSURANCE COMPANY\"),\n", "    (\"101907\", \"KEMPER INDEPENDENCE INSURANCE COMPANY\"),\n", "    (\"200933\", \"<PERSON>E<PERSON> RISK INSURANCE COMPANY\"),\n", "    (\"204308\", \"KIN INTERINSURANCE NETWORK\"),\n", "    (\"104696\", \"<PERSON>IN INTERINSURANCE NEXUS EXCHANGE\"),\n", "    (\"103366\", \"KINGSWAY AMIGO INSURANCE COMPANY\"),\n", "    (\"201993\", \"KNIGHT<PERSON><PERSON>OK INSURANCE COMPANY\"),\n", "    (\"100574\", \"LAMORAK INSURANCE COMPANY\"),\n", "    (\"101924\", \"LANCER INSURANCE COMPANY\"),\n", "    (\"203873\", \"LEMONADE INSURANCE COMPANY\"),\n", "    (\"102506\", \"LEXINGTON NATIONAL INSURANCE CORPORATION\"),\n", "    (\"103651\", \"LIBERTY AMERICAN INSURANCE COMPANY\"),\n", "    (\"103909\", \"LIBERTY AMERICAN SELECT INSURANCE COMPANY\"),\n", "    (\"100519\", \"LIBERTY INSURANCE CORPORATION\"),\n", "    (\"101131\", \"LIBERTY INSURANCE UNDERWRITERS INC.\"),\n", "    (\"100557\", \"LIBERTY MUTUAL FIRE INSURANCE COMPANY\"),\n", "    (\"100542\", \"LIBERTY MUTUAL INSURANCE COMPANY\"),\n", "    (\"101185\", \"LIBERTY MUTUAL MID-ATLANTIC INSURANCE COMPANY\"),\n", "    (\"100530\", \"LIBERTY MUTUAL PERSONAL INSURANCE COMPANY\"),\n", "    (\"204639\", \"LIGHTHOUSE PROPERTY INSURANCE CORPORATION\"),\n", "    (\"104827\", \"LIO INSURANCE COMPANY\"),\n", "    (\"100937\", \"LION INSURANCE COMPANY\"),\n", "    (\"101130\", \"LM GENERAL INSURANCE COMPANY\"),\n", "    (\"100600\", \"LM INSURANCE CORPORATION\"),\n", "    (\"101125\", \"LM PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"205160\", \"LOGGERHEAD RECIPROCAL INTERINSURANCE EXCHANGE\"),\n", "    (\"102253\", \"LUMBERMEN&#39;S UNDERWRITING ALLIANCE\"),\n", "    (\"201163\", \"LYNDON SOUTHERN INSURANCE COMPANY\"),\n", "    (\"102720\", \"MAG MUTUAL INSURANCE COMPANY\"),\n", "    (\"201077\", \"MAGNOLIA INSURANCE COMPANY\"),\n", "    (\"200890\", \"MAIN STREET AMERICA ASSURANCE COMPANY\"),\n", "    (\"201533\", \"MAIN STREET AMERICA PROTECTION INSURANCE COMPANY\"),\n", "    (\"205169\", \"MAINSAIL INSURANCE COMPANY\"),\n", "    (\"203685\", \"MAISON INSURANCE COMPANY\"),\n", "    (\"205444\", \"MANATEE INSURANCE EXCHANGE\"),\n", "    (\"105260\", \"MANUFACTURERS ALLIANCE INSURANCE COMPANY\"),\n", "    (\"103935\", \"MAPFRE INSURANCE COMPANY OF FLORIDA\"),\n", "    (\"102562\", \"MARKEL AMERICAN INSURANCE COMPANY\"),\n", "    (\"100755\", \"MARKEL GLOBAL REINSURANCE COMPANY\"),\n", "    (\"101779\", \"MARKEL INSURANCE COMPANY\"),\n", "    (\"102440\", \"MARYLAND CASUALTY COMPANY\"),\n", "    (\"100561\", \"MASSACHUSETTS BAY INSURANCE COMPANY\"),\n", "    (\"101559\", \"MEDICAL PROTECTIVE COMPANY (THE)\"),\n", "    (\"102809\", \"MEDMARC CASUALTY INSURANCE COMPANY\"),\n", "    (\"102154\", \"MENDOTA INSURANCE COMPANY\"),\n", "    (\"103772\", \"MERASTAR INSURANCE COMPANY\"),\n", "    (\"104801\", \"MERCURY CASUALTY COMPANY\"),\n", "    (\"105430\", \"MERCURY INDEMNITY COMPANY OF AMERICA\"),\n", "    (\"105429\", \"MERCURY INSURANCE COMPANY OF FLORIDA\"),\n", "    (\"100575\", \"METROMILE INSURANCE COMPANY\"),\n", "    (\"101142\", \"METROPOLITAN GENERAL INSURANCE COMPANY\"),\n", "    (\"104479\", \"MGA INSURANCE COMPANY, INC.\"),\n", "    (\"101600\", \"MIC GENERAL INSURANCE CORPORATION\"),\n", "    (\"102010\", \"MIC PROPERTY &amp; CASUALTY INS. CORP.\"),\n", "    (\"104865\", \"MID-CENTURY INSURANCE COMPANY\"),\n", "    (\"105475\", \"MID-CONTINENT ASSURANCE COMPANY\"),\n", "    (\"105476\", \"MID-CONTINENT CASUALTY COMPANY\"),\n", "    (\"100550\", \"MIDDLESEX INSURANCE COMPANY\"),\n", "    (\"101738\", \"MIDVALE INDEMNITY COMPANY\"),\n", "    (\"101425\", \"MIDWEST EMPLOYERS CASUALTY COMPANY\"),\n", "    (\"201284\", \"MIDWEST FAMILY MUTUAL INSURANCE COMPANY\"),\n", "    (\"204525\", \"MILFORD CASUALTY INSURANCE COMPANY\"),\n", "    (\"200598\", \"MITSUI SUMITOMO INSURANCE COMPANY OF AMERICA\"),\n", "    (\"100951\", \"MITSUI SUMITOMO INSURANCE USA INC.\"),\n", "    (\"201623\", \"MODERN USA INSURANCE COMPANY\"),\n", "    (\"203391\", \"MONARCH NATIONAL INSURANCE COMPANY\"),\n", "    (\"105581\", \"MONROE GUARANTY INSURANCE COMPANY\"),\n", "    (\"202002\", \"MONTGOMERY MUTUAL INSURANCE COMPANY\"),\n", "    (\"101973\", \"MOTORS INSURANCE CORPORATION\"),\n", "    (\"203127\", \"MOUNT BEACON INSURANCE COMPANY\"),\n", "    (\"201377\", \"MS TRANSVERSE INSURANCE COMPANY\"),\n", "    (\"102343\", \"NATIONAL AMERICAN INSURANCE COMPANY\"),\n", "    (\"104795\", \"NATIONAL AMERICAN INSURANCE COMPANY OF CALIFORNIA\"),\n", "    (\"102601\", \"NATIONAL BUILDERS INSURANCE COMPANY\"),\n", "    (\"101974\", \"NATIONAL CASUALTY COMPANY\"),\n", "    (\"102306\", \"NATIONAL FIRE AND INDEMNITY EXCHANGE\"),\n", "    (\"100650\", \"NATIONAL FIRE INSURANCE COMPANY OF HARTFORD\"),\n", "    (\"102259\", \"NATIONAL GENERAL INSURANCE COMPANY\"),\n", "    (\"102349\", \"NATIONAL INDEMNITY COMPANY\"),\n", "    (\"103240\", \"NATIONAL INDEMNITY COMPANY OF THE SOUTH\"),\n", "    (\"101526\", \"NATIONAL INTERSTATE INSURANCE COMPANY\"),\n", "    (\"103822\", \"NATIONAL SECURITY FIRE &amp; CASUALTY COMPANY\"),\n", "    (\"102950\", \"NATIONAL SPECIALTY INSURANCE COMPANY\"),\n", "    (\"204865\", \"NATIONAL SUMMIT INSURANCE COMPANY\"),\n", "    (\"101735\", \"NATIONAL SURETY CORPORATION\"),\n", "    (\"103768\", \"NATIONAL TRUST INSURANCE COMPANY\"),\n", "    (\"101343\", \"NATIONAL UNION FIRE INSURANCE CO. OF PITTSBURGH, PA\"),\n", "    (\"102388\", \"NATIONWIDE AFFINITY INSURANCE COMPANY OF AMERICA\"),\n", "    (\"102203\", \"NATIONWI<PERSON> AGRIBUSINESS INSURANCE COMPANY\"),\n", "    (\"104754\", \"NATIONWIDE ASSURANCE COMPANY\"),\n", "    (\"101468\", \"NATIONWIDE GENERAL INSURANCE COMPANY\"),\n", "    (\"104783\", \"NATIONWIDE INSURANCE COMPANY OF AMERICA\"),\n", "    (\"101446\", \"NATIONWIDE INSURANCE COMPANY OF FLORIDA\"),\n", "    (\"101451\", \"NATIONWIDE MUTUAL FIRE INSURANCE COMPANY\"),\n", "    (\"101450\", \"NATIONWIDE MUTUAL INSURANCE COMPANY\"),\n", "    (\"101409\", \"NATIONWIDE PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"100043\", \"NAU COUNTRY INSURANCE COMPANY\"),\n", "    (\"100921\", \"NAVIGATORS INSURANCE COMPANY\"),\n", "    (\"100558\", \"NEW ENGLAND INSURANCE COMPANY\"),\n", "    (\"104989\", \"NEW HAMPSHIRE INSURANCE COMPANY\"),\n", "    (\"100867\", \"NEW YORK MARINE AND GENERAL INSURANCE COMPANY\"),\n", "    (\"204650\", \"NEXT INSURANCE US COMPANY\"),\n", "    (\"100488\", \"NGM INSURANCE COMPANY\"),\n", "    (\"200490\", \"NORGUARD INSURANCE COMPANY\"),\n", "    (\"102022\", \"NORTH POINTE INSURANCE COMPANY\"),\n", "    (\"101119\", \"NORTH RIVER INSURANCE COMPANY\"),\n", "    (\"200884\", \"NORTHERN CAPITAL INSURANCE COMPANY\"),\n", "    (\"201688\", \"NORTHERN CAPITAL SELECT INSURANCE COMPANY\"),\n", "    (\"100998\", \"NORTHERN INSURANCE COMPANY OF NEW YORK\"),\n", "    (\"104748\", \"NORTHLAND CASUALTY COMPANY\"),\n", "    (\"102166\", \"NORTHLAND INSURANCE COMPANY\"),\n", "    (\"101071\", \"NOVA CASUALTY COMPANY\"),\n", "    (\"103773\", \"OAKWOOD INSURANCE COMPANY\"),\n", "    (\"204735\", \"OBSIDIAN INSURANCE COMPANY\"),\n", "    (\"104550\", \"<PERSON><PERSON><PERSON><PERSON>TAL FIRE AND CASUALTY COMPANY OF NC\"),\n", "    (\"100938\", \"OCEAN HARBOR CASUALTY INSURANCE COMPANY\"),\n", "    (\"102375\", \"ODYSSEY REINSURANCE COMPANY\"),\n", "    (\"101367\", \"OHIO CASUALTY INSURANCE COMPANY\"),\n", "    (\"101494\", \"OHIO FARMERS INSURANCE COMPANY\"),\n", "    (\"101377\", \"OHIO INDEMNITY COMPANY\"),\n", "    (\"105083\", \"OHIO SECURITY INSURANCE COMPANY\"),\n", "    (\"103164\", \"OLD DOMINION INSURANCE COMPANY\"),\n", "    (\"204696\", \"OLD GUARD INSURANCE COMPANY\"),\n", "    (\"101918\", \"OLD REPUBLIC GENERAL INSURANCE CORPORATION\"),\n", "    (\"101336\", \"OLD REPUBLIC INSURANCE COMPANY\"),\n", "    (\"104323\", \"OLD REPUBLIC SECURITY ASSURANCE COMPANY\"),\n", "    (\"201612\", \"OLYMPUS INSURANCE COMPANY\"),\n", "    (\"103122\", \"OMEGA INSURANCE COMPANY\"),\n", "    (\"205404\", \"ORANGE INSURANCE EXCHANGE\"),\n", "    (\"205352\", \"ORION180 SELECT INSURANCE COMPANY\"),\n", "    (\"205522\", \"OVATION HOME INSURANCE EXCHANGE\"),\n", "    (\"101513\", \"OWNERS INSURANCE COMPANY\"),\n", "    (\"104759\", \"PACIFI<PERSON> EMPLOYERS INSURANCE COMPANY\"),\n", "    (\"104760\", \"PACIFIC INDEMNITY COMPANY\"),\n", "    (\"105058\", \"PACIFIC SPECIALTY INSURANCE COMPANY\"),\n", "    (\"201613\", \"<PERSON><PERSON> BEACH WINDSTORM SELF INSURANCE TRUST\"),\n", "    (\"201008\", \"PARK NATIONAL INSURANCE COMPANY\"),\n", "    (\"100548\", \"PARTNERRE AMERICA INSURANCE COMPANY\"),\n", "    (\"100570\", \"PATRIOT GENERAL INSURANCE COMPANY\"),\n", "    (\"203357\", \"PATRIOT SELECT PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"102622\", \"PEAK PROPERTY AND CASUALTY INSURANCE CORP.\"),\n", "    (\"100892\", \"PEERLESS INDEMNITY INSURANCE COMPANY\"),\n", "    (\"100489\", \"PEERLESS INSURANCE COMPANY\"),\n", "    (\"103902\", \"PELEUS INSURANCE COMPANY\"),\n", "    (\"101333\", \"PENN MILLERS INSURANCE COMPANY\"),\n", "    (\"101213\", \"PENNSYLVANIA INSURANCE COMPANY\"),\n", "    (\"101193\", \"PENNSYLVANIA LUMBERMENS MUTUAL INSURANCE COMPANY\"),\n", "    (\"101237\", \"PENNSYLVANIA MANUFACTURERS&#39; ASSOCIATION INS. CO.\"),\n", "    (\"105259\", \"PENNSYLVANIA MANUFACTURERS INDEMNITY COMPANY\"),\n", "    (\"101194\", \"PENNSYLVANIA NATIONAL MUTUAL CASUALTY INSURANCE CO\"),\n", "    (\"201695\", \"PEOPLE&#39;S TRUST INSURANCE COMPANY\"),\n", "    (\"202229\", \"PHARMACISTS MUTUAL INSURANCE COMPANY\"),\n", "    (\"101244\", \"PH<PERSON>ADELPHIA INDEMNITY INSURANCE COMPANY\"),\n", "    (\"101083\", \"PIE INSURANCE COMPANY (THE)\"),\n", "    (\"101551\", \"PINNACLE NATIONAL INSURANCE COMPANY\"),\n", "    (\"202877\", \"PLATEAU CASUALTY INSURANCE COMPANY\"),\n", "    (\"102608\", \"PLATTE RIVER INSURANCE COMPANY\"),\n", "    (\"102702\", \"PLAZA INSURANCE COMPANY\"),\n", "    (\"100970\", \"PLYMOUTH ROCK ASSURANCE PREFERRED CORPORATION\"),\n", "    (\"104687\", \"POINT SPECIALTY INSURANCE COMPANY\"),\n", "    (\"101775\", \"PRAETORIAN INSURANCE COMPANY\"),\n", "    (\"202041\", \"PREPARED INSURANCE COMPANY\"),\n", "    (\"201490\", \"PRIVILEGE UNDERWRITERS RECIPROCAL EXCHANGE\"),\n", "    (\"200836\", \"PRODUCERS AGRICULTURE INSURANCE COMPANY\"),\n", "    (\"102494\", \"PROFESSIONALS ADVOCATE INSURANCE COMPANY\"),\n", "    (\"103757\", \"PROGRESSIVE ADVANCED INSURANCE COMPANY\"),\n", "    (\"101511\", \"PROGRESSIVE AMERICAN INSURANCE COMPANY\"),\n", "    (\"101542\", \"PROGRESSIVE CASUALTY INSURANCE COMPANY\"),\n", "    (\"103584\", \"PROGRESSIVE EXPRESS INSURANCE COMPANY\"),\n", "    (\"201643\", \"PROGRESSIVE PROPERTY INSURANCE COMPANY\"),\n", "    (\"103135\", \"PROGRESSIVE SOUTHEASTERN INSURANCE COMPANY\"),\n", "    (\"100724\", \"PROPERTY &amp; CASUALTY INSURANCE COMPANY OF HARTFORD\"),\n", "    (\"101638\", \"PROTECTIVE INSURANCE COMPANY\"),\n", "    (\"102272\", \"PROTECTIVE PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"100795\", \"PUBLIC SERVICE INSURANCE COMPANY\"),\n", "    (\"101138\", \"QBE INSURANCE CORPORATION\"),\n", "    (\"101707\", \"R.V.I. AMERICA INSURANCE COMPANY\"),\n", "    (\"101256\", \"RADIAN MORTGAGE ASSURANCE INC\"),\n", "    (\"102091\", \"REGENT INSURANCE COMPANY\"),\n", "    (\"205289\", \"REPU<PERSON><PERSON> FIRE AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"100593\", \"RESPONSE INSURANCE COMPANY\"),\n", "    (\"103748\", \"RESPONS<PERSON> WORLDWIDE DIRECT AUTO INSURANCE COMPANY\"),\n", "    (\"102075\", \"RESPONS<PERSON> WORLDWIDE INSURANCE COMPANY\"),\n", "    (\"102156\", \"RIVERPORT INSURANCE COMPANY\"),\n", "    (\"100569\", \"RIVERSTONE INTERNATIONAL INSURANCE, INC.\"),\n", "    (\"101944\", \"RLI INSURANCE COMPANY\"),\n", "    (\"205278\", \"ROCHDALE INSURANCE COMPANY\"),\n", "    (\"204422\", \"ROCK RIDGE INSURANCE COMPANY\"),\n", "    (\"101594\", \"ROCKWOOD CASUALTY INSURANCE COMPANY\"),\n", "    (\"204629\", \"ROOT INSURANCE COMPANY\"),\n", "    (\"202700\", \"ROOT PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"101066\", \"RSUI INDEMNITY COMPANY\"),\n", "    (\"201237\", \"RURAL COMMUNITY INSURANCE COMPANY\"),\n", "    (\"104504\", \"RURAL TRUST INSURANCE COMPANY\"),\n", "    (\"201255\", \"SAFE HARBOR INSURANCE COMPANY\"),\n", "    (\"104664\", \"SAFECO INSURANCE COMPANY OF AMERICA\"),\n", "    (\"203106\", \"SAFEPOINT INSURANCE COMPANY\"),\n", "    (\"103092\", \"SAFEPORT INSURANCE COMPANY\"),\n", "    (\"102248\", \"SAFETY NATIONAL CASUALTY CORPORATION\"),\n", "    (\"202497\", \"SAMSUNG FIRE &amp; MARINE INSURANCE CO., LTD. (US BRANCH)\"),\n", "    (\"201594\", \"SAWGRASS MUTUAL INSURANCE COMPANY\"),\n", "    (\"101423\", \"SCOTTSDALE INDEMNITY COMPANY\"),\n", "    (\"201172\", \"SECURIAN CASUALTY COMPANY\"),\n", "    (\"200981\", \"SECURITY FIRST INSURANCE COMPANY\"),\n", "    (\"104510\", \"SELECT INSURANCE COMPANY\"),\n", "    (\"102614\", \"SELECTIVE INSURANCE COMPANY OF THE SOUTHEAST\"),\n", "    (\"100896\", \"SENECA INSURANCE COMPANY, INC.\"),\n", "    (\"105225\", \"SENTINEL INSURANCE COMPANY, LTD.\"),\n", "    (\"200807\", \"SENTRY CASUALTY COMPANY\"),\n", "    (\"102039\", \"SENTRY INSURANCE COMPANY\"),\n", "    (\"101730\", \"SENTRY SELECT INSURANCE COMPANY\"),\n", "    (\"103159\", \"SERVICE AMERICAN INDEMNITY COMPANY\"),\n", "    (\"102695\", \"SHIELD INSURANCE COMPANY\"),\n", "    (\"100900\", \"SIRIUSPOINT AMERICA INSURANCE COMPANY\"),\n", "    (\"205025\", \"SLIDE INSURANCE COMPANY\"),\n", "    (\"100879\", \"SOMPO AMERICA FIRE &amp; MARINE INSURANCE COMPANY\"),\n", "    (\"100848\", \"SOMPO AMERICA INSURANCE COMPANY\"),\n", "    (\"103852\", \"SOUTHERN FARM BUREAU CASUALTY INSURANCE COMPANY\"),\n", "    (\"200989\", \"SOUTHERN FIDELITY INSURANCE COMPANY\"),\n", "    (\"202686\", \"SOUTHERN FIDELITY PROPERTY &amp; CASUALTY, INC.\"),\n", "    (\"104516\", \"SOUTHERN INSURANCE COMPANY\"),\n", "    (\"200925\", \"SOUTHERN OAK INSURANCE COMPANY\"),\n", "    (\"204895\", \"SOUTHERN VANGUARD INSURANCE COMPANY\"),\n", "    (\"103601\", \"SOUTHERN-OWNERS INSURANCE COMPANY\"),\n", "    (\"100531\", \"SPARTA INSURANCE COMPANY\"),\n", "    (\"203673\", \"SPINNAKER INSURANCE COMPANY\"),\n", "    (\"200699\", \"ST. JOHNS INSURANCE COMPANY, INC.\"),\n", "    (\"102098\", \"ST. PAUL FIRE AND MARINE INSURANCE COMPANY\"),\n", "    (\"102124\", \"ST. PAUL GUARDIAN INSURANCE COMPANY\"),\n", "    (\"102119\", \"ST. PAUL MERCURY INSURANCE COMPANY\"),\n", "    (\"101712\", \"ST. PAUL PROTECTIVE INSURANCE COMPANY\"),\n", "    (\"102730\", \"STANDARD GUARANTY INSURANCE COMPANY\"),\n", "    (\"202060\", \"STAR &amp; SHIELD INSURANCE EXCHANGE\"),\n", "    (\"102020\", \"STAR INSURANCE COMPANY\"),\n", "    (\"101172\", \"STARNET INSURANCE COMPANY\"),\n", "    (\"104472\", \"STARR INDEMNITY &amp; LIABILITY COMPANY\"),\n", "    (\"104762\", \"STARSTONE NATIONAL INSURANCE COMPANY\"),\n", "    (\"102671\", \"STATE AUTO PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"101460\", \"STATE AUTOMOBILE MUTUAL INSURANCE COMPANY\"),\n", "    (\"101934\", \"STATE FARM FIRE AND CASUALTY COMPANY\"),\n", "    (\"101910\", \"STATE FARM FLORIDA INSURANCE COMPANY\"),\n", "    (\"101941\", \"STATE FARM GENERAL INSURANCE COMPANY\"),\n", "    (\"104486\", \"STATE NATIONAL INSURANCE COMPANY INC.\"),\n", "    (\"200241\", \"STILLWATER INSURANCE COMPANY\"),\n", "    (\"101068\", \"STILLWATER PROPERTY AND CASUALTY INSURANCE COMPANY\"),\n", "    (\"203992\", \"STONEWOOD INSURANCE COMPANY\"),\n", "    (\"102646\", \"STONINGTON INSURANCE COMPANY\"),\n", "    (\"100500\", \"STRATFORD INSURANCE COMPANY\"),\n", "    (\"103661\", \"SUNSHINE STATE INSURANCE COMPANY\"),\n", "    (\"102665\", \"SUSSEX INSURANCE COMPANY\"),\n", "    (\"104666\", \"SUTTON NATIONAL INSURANCE COMPANY\"),\n", "    (\"100498\", \"SWISS RE CORPORATE SOLUTIONS AMERICA INSURANCE CORPORATION\"),\n", "    (\"100949\", \"SWISS RE CORPORATE SOLUTIONS ELITE INSURANCE CORPORATION\"),\n", "    (\"101762\", \"SWISS RE CORPORATE SOLUTIONS PREMIER INSURANCE CORPORATION\"),\n", "    (\"100571\", \"T.H.E. INSURANCE COMPANY\"),\n", "    (\"205614\", \"TA<PERSON>ROW INSURANCE EXCHANGE\"),\n", "    (\"101246\", \"TEACHERS INSURANCE COMPANY\"),\n", "    (\"200551\", \"TECHNOLOGY INSURANCE COMPANY, INC\"),\n", "    (\"104866\", \"TESLA INSURANCE COMPANY\"),\n", "    (\"104782\", \"TESLA PROPERTY &amp; CASUALTY, INC.\"),\n", "    (\"100670\", \"THE AUTOMOBILE INSURANCE COMPANY OF HARTFORD, CONNECTICUT\"),\n", "    (\"100637\", \"THE CHARTER OAK FIRE INSURANCE COMPANY\"),\n", "    (\"101392\", \"THE CINCINNATI CASUALTY COMPANY\"),\n", "    (\"101435\", \"THE CINCINNATI INDEMNITY COMPANY\"),\n", "    (\"101375\", \"THE CINCINNATI INSURANCE COMPANY\"),\n", "    (\"100639\", \"THE PHOENIX INSURANCE COMPANY\"),\n", "    (\"100763\", \"THE STANDARD FIRE INSURANCE COMPANY\"),\n", "    (\"102142\", \"THE TRAVELERS CASUALTY COMPANY\"),\n", "    (\"100654\", \"THE TRAVELERS INDEMNITY COMPANY\"),\n", "    (\"102791\", \"THE TRAVELERS INDEMNITY COMPANY OF AMERICA\"),\n", "    (\"100643\", \"THE TRAVELERS INDEMNITY COMPANY OF CONNECTICUT\"),\n", "    (\"104713\", \"TIG INSURANCE COMPANY\"),\n", "    (\"104393\", \"TITAN INDEMNITY COMPANY\"),\n", "    (\"200899\", \"TNUS INSURANCE COMPANY\"),\n", "    (\"204575\", \"TOGGLE INSURANCE COMPANY\"),\n", "    (\"202918\", \"TOKIO MARINE AMERICA INSURANCE COMPANY\"),\n", "    (\"101039\", \"TOKIO MARINE AND NICHIDO FIRE INS. CO., LTD. (US BRANCH)\"),\n", "    (\"204981\", \"TOWER HILL INSURANCE EXCHANGE\"),\n", "    (\"102625\", \"TOWER HILL PREFERRED INSURANCE COMPANY\"),\n", "    (\"105063\", \"TOWER HILL PRIME INSURANCE COMPANY\"),\n", "    (\"200761\", \"TOWER HILL SELECT INSURANCE COMPANY\"),\n", "    (\"201305\", \"TOWER HILL SIGNATURE INSURANCE COMPANY\"),\n", "    (\"100917\", \"TRANS PACIFIC INSURANCE COMPANY\"),\n", "    (\"101833\", \"TRANSGUARD INSURANCE COMPANY OF AMERICA, INC.\"),\n", "    (\"101676\", \"TRANSPORTATION INSURANCE COMPANY\"),\n", "    (\"101625\", \"TRAVCO INSURANCE COMPANY\"),\n", "    (\"101771\", \"TRAVCO PERSONAL INSURANCE COMPANY\"),\n", "    (\"100762\", \"TRAVELERS CASUALTY AND SURETY COMPANY\"),\n", "    (\"100677\", \"TRAVELERS CASUALTY AND SURETY COMPANY OF AMERICA\"),\n", "    (\"100728\", \"TRAVELERS CASUALTY COMPANY OF CONNECTICUT\"),\n", "    (\"100672\", \"TRAVELERS CASUALTY INSURANCE COMPANY OF AMERICA\"),\n", "    (\"104835\", \"TRAVELERS COMMERCIAL CASUALTY COMPANY\"),\n", "    (\"100729\", \"TRAVELERS COMMERCIAL INSURANCE COMPANY\"),\n", "    (\"101626\", \"TRAVELERS HOME AND MARINE INSURANCE COMPANY(THE)\"),\n", "    (\"101741\", \"TRAVELERS PROPERTY CASUALTY COMPANY OF AMERICA\"),\n", "    (\"100731\", \"TRAVELERS PROPERTY CASUALTY INSURANCE COMPANY\"),\n", "    (\"205564\", \"TRIDENT RECIPROCAL EXCHANGE\"),\n", "    (\"203678\", \"TRI-STATE INSURANCE COMPANY OF MINNESOTA\"),\n", "    (\"104806\", \"TRISURA INSURANCE COMPANY\"),\n", "    (\"103195\", \"TRITON INSURANCE COMPANY\"),\n", "    (\"202278\", \"TRIUMPHE CASUALTY COMPANY\"),\n", "    (\"104799\", \"TRUCK INSURANCE EXCHANGE\"),\n", "    (\"204638\", \"TRUSTED RESOURCE UNDERWRITERS EXCHANGE\"),\n", "    (\"100665\", \"TWIN CITY FIRE INSURANCE COMPANY\"),\n", "    (\"203626\", \"TYPTAP INSURANCE COMPANY\"),\n", "    (\"102497\", \"U.S. SPECIALTY INSURANCE COMPANY\"),\n", "    (\"203089\", \"U.S. UNDERWRITERS INSURANCE COMPANY\"),\n", "    (\"104357\", \"UFG SPECIALTY INSURANCE COMPANY\"),\n", "    (\"203461\", \"UNION INSURANCE COMPANY\"),\n", "    (\"201726\", \"UNIQUE INSURANCE COMPANY\"),\n", "    (\"205398\", \"UNITED BUILDERS INSURANCE COMPANY\"),\n", "    (\"101225\", \"UNITED CASUALTY INSURANCE COMPANY OF AMERICA\"),\n", "    (\"101794\", \"UNITED FINANCIAL CASUALTY COMPANY\"),\n", "    (\"104412\", \"UNITED FIRE &amp; INDEMNITY COMPANY\"),\n", "    (\"102185\", \"UNITED FIRE AND CASUALTY COMPANY\"),\n", "    (\"105012\", \"UNITED PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"104355\", \"UNITED SERVICES AUTOMOBILE ASSOCIATION\"),\n", "    (\"102443\", \"UNITED STATES FIDELITY AND GUARANTY COMPANY\"),\n", "    (\"101006\", \"UNITED STATES FIRE INSURANCE COMPANY\"),\n", "    (\"101207\", \"UNITED STATES LIABILITY INSURANCE COMPANY\"),\n", "    (\"102508\", \"UNITRIN AUTO AND HOME INSURANCE COMPANY\"),\n", "    (\"200803\", \"UNIVERSAL FIRE &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"200400\", \"UNIVERSAL INSURANCE COMPANY\"),\n", "    (\"200786\", \"UNIVERSAL INSURANCE COMPANY OF NORTH AMERICA\"),\n", "    (\"204046\", \"UNIVERSAL NORTH AMERICA INSURANCE COMPANY\"),\n", "    (\"104219\", \"UNIVERSAL PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"102287\", \"UNIVERSAL UNDERWRITERS INSURANCE COMPANY\"),\n", "    (\"203546\", \"US COASTAL PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"103529\", \"USAA CASUALTY INSURANCE COMPANY\"),\n", "    (\"104375\", \"USAA GENERAL INDEMNITY COMPANY\"),\n", "    (\"200605\", \"USIC OF FLORIDA, INC.\"),\n", "    (\"101061\", \"UTICA FIRST INSURANCE COMPANY\"),\n", "    (\"101062\", \"UTICA MUTUAL INSURANCE COMPANY\"),\n", "    (\"101229\", \"VALLEY FORGE INSURANCE COMPANY\"),\n", "    (\"104580\", \"VANLINER INSURANCE COMPANY\"),\n", "    (\"101758\", \"VANTAGE RISK ASSURANCE COMPANY\"),\n", "    (\"203627\", \"VANTAPRO SPECIALTY INSURANCE COMPANY\"),\n", "    (\"203919\", \"VAULT RECIPROCAL EXCHANGE\"),\n", "    (\"202018\", \"VERLAN FIRE INSURANCE COMPANY\"),\n", "    (\"101522\", \"VICTORIA FIRE &amp; CASUALTY COMPANY\"),\n", "    (\"100836\", \"VIGILANT INSURANCE COMPANY\"),\n", "    (\"101790\", \"VIRGINIA SURETY COMPANY, INC.\"),\n", "    (\"204987\", \"VYRD INSURANCE COMPANY\"),\n", "    (\"101811\", \"WARNER INSURANCE COMPANY\"),\n", "    (\"101830\", \"<PERSON><PERSON><PERSON><PERSON> BUSINESS INSURANCE COMPANY\"),\n", "    (\"102076\", \"WAUSAU UNDERWRITERS INSURANCE COMPANY\"),\n", "    (\"101105\", \"WCF SELECT INSURANCE COMPANY\"),\n", "    (\"104846\", \"WELLFLEET INSURANCE COMPANY\"),\n", "    (\"104576\", \"WESCO INSURANCE COMPANY\"),\n", "    (\"101378\", \"WEST AMERICAN INSURANCE COMPANY\"),\n", "    (\"104690\", \"WESTCHESTER FIRE INSURANCE COMPANY\"),\n", "    (\"101543\", \"WESTFIELD INSURANCE COMPANY\"),\n", "    (\"101507\", \"WESTFIELD NATIONAL INS. COMPANY\"),\n", "    (\"205233\", \"WESTGUARD INSURANCE COMPANY\"),\n", "    (\"202707\", \"WESTON INSURANCE COMPANY\"),\n", "    (\"201213\", \"WESTON PROPERTY &amp; CASUALTY INSURANCE COMPANY\"),\n", "    (\"102403\", \"WESTPORT INSURANCE CORPORATION\"),\n", "    (\"202857\", \"WHITE PINE INSURANCE COMPANY\"),\n", "    (\"104757\", \"WORKMEN&#39;S AUTO INSURANCE COMPANY\"),\n", "    (\"202006\", \"WRIGHT NATIONAL FLOOD INSURANCE COMPANY\"),\n", "    (\"104512\", \"XL INSURANCE AMERICA, INC.\"),\n", "    (\"100797\", \"XL REINSURANCE AMERICA INC.\"),\n", "    (\"104577\", \"XL SPECIALTY INSURANCE COMPANY\"),\n", "    (\"104715\", \"YOSEMITE INSURANCE COMPANY\"),\n", "    (\"104769\", \"ZENITH INSURANCE COMPANY\"),\n", "    (\"101908\", \"ZURICH AMERICAN INSURANCE COMPANY\"),\n", "    (\"101752\", \"ZURICH AMERICAN INSURANCE COMPANY OF ILLINOIS\"),\n", "]\n", "\n", "counties = [\n", "    (\"11\", \"Alachua\"),\n", "    (\"52\", \"<PERSON>\"),\n", "    (\"23\", \"Bay\"),\n", "    (\"45\", \"Bradford\"),\n", "    (\"19\", \"<PERSON><PERSON>vard\"),\n", "    (\"10\", \"Broward\"),\n", "    (\"58\", \"<PERSON>\"),\n", "    (\"53\", \"<PERSON>\"),\n", "    (\"47\", \"Citrus\"),\n", "    (\"48\", \"<PERSON>\"),\n", "    (\"64\", \"<PERSON>\"),\n", "    (\"29\", \"Columbia\"),\n", "    (\"01\", \"<PERSON><PERSON>\"),\n", "    (\"34\", \"<PERSON>oto\"),\n", "    (\"54\", \"Dixie\"),\n", "    (\"02\", \"<PERSON>\"),\n", "    (\"09\", \"Escambia\"),\n", "    (\"61\", \"Flagler\"),\n", "    (\"59\", \"<PERSON>\"),\n", "    (\"21\", \"Gadsden\"),\n", "    (\"55\", \"Gil<PERSON><PERSON>\"),\n", "    (\"60\", \"Glades\"),\n", "    (\"66\", \"Gulf\"),\n", "    (\"56\", \"Hamilton\"),\n", "    (\"30\", \"Hardee\"),\n", "    (\"49\", \"<PERSON><PERSON><PERSON>\"),\n", "    (\"40\", \"<PERSON><PERSON><PERSON>\"),\n", "    (\"27\", \"Highlands\"),\n", "    (\"03\", \"Hillsborough\"),\n", "    (\"51\", \"<PERSON>\"),\n", "    (\"32\", \"Indian River\"),\n", "    (\"25\", \"<PERSON>\"),\n", "    (\"46\", \"<PERSON>\"),\n", "    (\"62\", \"Lafayette\"),\n", "    (\"12\", \"Lake\"),\n", "    (\"18\", \"<PERSON>\"),\n", "    (\"13\", \"<PERSON>\"),\n", "    (\"39\", \"<PERSON>\"),\n", "    (\"67\", \"Liberty\"),\n", "    (\"35\", \"Madison\"),\n", "    (\"15\", \"Manatee\"),\n", "    (\"14\", \"<PERSON>\"),\n", "    (\"42\", \"<PERSON>\"),\n", "    (\"38\", \"<PERSON>\"),\n", "    (\"41\", \"Nassau\"),\n", "    (\"43\", \"Okaloosa\"),\n", "    (\"57\", \"Okeechobee\"),\n", "    (\"07\", \"Orange\"),\n", "    (\"26\", \"Osce<PERSON>\"),\n", "    (\"00\", \"Out of state\"),\n", "    (\"06\", \"Palm Beach\"),\n", "    (\"28\", \"Pasco\"),\n", "    (\"04\", \"Pinellas\"),\n", "    (\"05\", \"Polk\"),\n", "    (\"22\", \"<PERSON>\"),\n", "    (\"33\", \"Santa Rosa\"),\n", "    (\"16\", \"<PERSON><PERSON><PERSON>\"),\n", "    (\"17\", \"Seminole\"),\n", "    (\"20\", \"St. Johns\"),\n", "    (\"24\", \"St. Lucie\"),\n", "    (\"44\", \"Sumter\"),\n", "    (\"31\", \"Suwannee\"),\n", "    (\"37\", \"<PERSON>\"),\n", "    (\"63\", \"Union\"),\n", "    (\"08\", \"Volusia\"),\n", "    (\"65\", \"<PERSON><PERSON><PERSON><PERSON>\"),\n", "    (\"36\", \"<PERSON>\"),\n", "    (\"50\", \"Washington\"),\n", "]\n", "\n", "print(f\"🔧 修复版本：将运行 {len(insurers)} 家公司 × {len(counties)} 个县，共 {len(insurers)*len(counties)} 次\")\n", "\n", "# ==== 启动 Selenium ChromeDriver ====\n", "service = Service(CHROME_DRIVER_PATH)\n", "options = webdriver.ChromeOptions()\n", "options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": DOWNLOAD_DIR,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "})\n", "\n", "driver = webdriver.Chrome(service=service, options=options)\n", "driver.execute_cdp_cmd(\"Page.setDownloadBehavior\", {\"behavior\": \"allow\", \"downloadPath\": DOWNLOAD_DIR})\n", "wait = WebDriverWait(driver, 30)\n", "\n", "try:\n", "    # 1. 打开查询条件页面\n", "    driver.get(\"https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx\")\n", "    driver.maximize_window()\n", "\n", "    # 2. 基本设置\n", "    wait.until(EC.element_to_be_clickable((By.ID, \"ctl00_ContentPlaceHolder1_radReportType_2\"))).click()\n", "    time.sleep(0.3)\n", "    \n", "    Select(wait.until(EC.presence_of_element_located((By.ID, \"ctl00_ContentPlaceHolder1_ddlFilingType\")))).select_by_value(\"1\")\n", "    time.sleep(0.3)\n", "    \n", "    wait.until(EC.element_to_be_clickable((By.ID, \"ctl00_ContentPlaceHolder1_lstReportingPeriod_MoveAllRight\"))).click()\n", "    time.sleep(0.3)\n", "\n", "    # ==== 主循环：Insurer × County ====\n", "    for ins_val, ins_name in insurers:\n", "        # 选择保险公司\n", "        driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstInsurers_MoveLeft\").click()\n", "        time.sleep(2)\n", "        driver.find_element(By.CSS_SELECTOR, f\"#ctl00_ContentPlaceHolder1_lstInsurers option[value='{ins_val}']\").click()\n", "        driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstInsurers_MoveRight\").click()\n", "        time.sleep(2)\n", "        print(f\"[公司] {ins_name}\")\n", "\n", "        # 全选选项\n", "        driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstPolicyType_MoveAllRight\").click()\n", "        time.sleep(2)\n", "        driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstDataElements_MoveAllRight\").click()\n", "        time.sleep(2)\n", "\n", "        for ct_val, ct_name in counties:\n", "            # 选择县区\n", "            driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstCounties_MoveLeft\").click()\n", "            time.sleep(2)\n", "            driver.find_element(By.CSS_SELECTOR, f\"#ctl00_ContentPlaceHolder1_lstCounties option[value='{ct_val}']\").click()\n", "            time.sleep(2)\n", "            driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstCounties_MoveRight\").click()\n", "            time.sleep(2)\n", "            print(f\"  [县] {ct_name}\")\n", "            \n", "            # ==== 搜索和导出 - 增强版窗口等待 ====\n", "            driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_btnSearch\").click()\n", "            main_handle = driver.current_window_handle\n", "            \n", "            # 🚀 使用智能重试机制等待新窗口\n", "            window_opened = False\n", "            for attempt in range(3):  # 最多重试3次\n", "                try:\n", "                    print(f\"    🔄 等待新窗口 (第{attempt+1}/3次)\")\n", "                    \n", "                    for i in range(45):  # 每次尝试等待45秒\n", "                        current_windows = len(driver.window_handles)\n", "                        if current_windows > 1:\n", "                            print(f\"    ✅ 第{i+1}秒检测到新窗口！\")\n", "                            new_handle = [h for h in driver.window_handles if h != main_handle][0]\n", "                            driver.switch_to.window(new_handle)\n", "                            window_opened = True\n", "                            break\n", "                        \n", "                        if i % 5 == 0:  # 每5秒显示一次进度\n", "                            print(f\"    ⏳ 等待中... ({i+1}/45秒)\")\n", "                        time.sleep(1)\n", "                    \n", "                    if window_opened:\n", "                        break\n", "                    else:\n", "                        print(f\"    ⚠️ 第{attempt+1}次尝试超时\")\n", "                        if attempt < 2:  # 还有重试机会\n", "                            print(f\"    🔄 准备重试...\")\n", "                            time.sleep(3)\n", "                            \n", "                except Exception as e:\n", "                    print(f\"    ❌ 第{attempt+1}次尝试出错: {e}\")\n", "                    if attempt < 2:\n", "                        time.sleep(3)\n", "                        \n", "            if not window_opened:\n", "                print(f\"    ❌ 所有重试都失败，跳过 {ins_name} — {ct_name}\")\n", "                continue\n", "            \n", "            # 等待页面加载完成\n", "            wait.until(lambda d: d.execute_script(\"return document.readyState\") == \"complete\")\n", "            print(f\"    📄 页面基础加载完成\")\n", "            \n", "            # 🚀 关键改进：等待导出按钮激活\n", "            print(f\"    ⏳ 等待导出功能激活...\")\n", "            \n", "            def is_export_button_enabled():\n", "                try:\n", "                    export_img = driver.find_element(By.ID, \"rptViewer_ctl05_ctl04_ctl00_ButtonImg\")\n", "                    src = export_img.get_attribute(\"src\")\n", "                    return \"ExportDisabled\" not in src\n", "                except:\n", "                    return False\n", "            \n", "            # 等待导出按钮激活\n", "            button_activated = False\n", "            for i in range(60):\n", "                if is_export_button_enabled():\n", "                    button_activated = True\n", "                    print(f\"    ✅ 导出按钮已激活\")\n", "                    break\n", "                if i % 10 == 0:\n", "                    print(f\"    ⏳ 等待激活... ({i+1}/60秒)\")\n", "                time.sleep(1)\n", "            \n", "            if not button_activated:\n", "                print(f\"    ⚠️ 按钮未激活，尝试强制操作\")\n", "            \n", "            # 🎯 尝试导出\n", "            export_success = False\n", "            \n", "            # 方法1：JavaScript API\n", "            try:\n", "                print(f\"    🎯 方法1：直接API调用...\")\n", "                result = driver.execute_script(\"\"\"\n", "                    try {\n", "                        var reportViewer = $find('rptViewer');\n", "                        if (report<PERSON>iewer) {\n", "                            reportViewer.exportReport('CSV');\n", "                            return 'success';\n", "                        } else {\n", "                            return 'reportViewer_not_found';\n", "                        }\n", "                    } catch (e) {\n", "                        return 'error: ' + e.message;\n", "                    }\n", "                \"\"\")\n", "                \n", "                if result == 'success':\n", "                    export_success = True\n", "                    print(f\"    ✅ 方法1成功\")\n", "                else:\n", "                    print(f\"    ❌ 方法1失败: {result}\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"    ❌ 方法1异常: {e}\")\n", "            \n", "            # 方法2：模拟点击\n", "            if not export_success:\n", "                try:\n", "                    print(f\"    🎯 方法2：模拟点击...\")\n", "                    export_btn = driver.find_element(By.ID, \"rptViewer_ctl05_ctl04_ctl00_ButtonLink\")\n", "                    driver.execute_script(\"arguments[0].click();\", export_btn)\n", "                    time.sleep(2)\n", "                    \n", "                    # 查找CSV选项\n", "                    csv_selectors = [\n", "                        \"//a[@title='CSV (comma delimited)']\",\n", "                        \"//a[contains(text(), 'CSV')]\",\n", "                        \"//a[contains(@onclick, 'CSV')]\"\n", "                    ]\n", "                    \n", "                    csv_found = False\n", "                    for selector in csv_selectors:\n", "                        for wait_time in range(10):\n", "                            try:\n", "                                csv_item = driver.find_element(By.XPATH, selector)\n", "                                driver.execute_script(\"arguments[0].click();\", csv_item)\n", "                                export_success = True\n", "                                csv_found = True\n", "                                print(f\"    ✅ 方法2成功\")\n", "                                break\n", "                            except:\n", "                                time.sleep(0.5)\n", "                        if csv_found:\n", "                            break\n", "                    \n", "                    if not csv_found:\n", "                        print(f\"    ❌ 未找到CSV选项\")\n", "                        \n", "                except Exception as e:\n", "                    print(f\"    ❌ 方法2失败: {e}\")\n", "            \n", "            # 处理下载结果\n", "            if export_success:\n", "                print(f\"    ⏳ 等待下载完成...\")\n", "                time.sleep(8)\n", "                \n", "                try:\n", "                    download_files = [f for f in os.listdir(DOWNLOAD_DIR) if f.endswith('.csv')]\n", "                    if download_files:\n", "                        latest_file = max([os.path.join(DOWNLOAD_DIR, f) for f in download_files], key=os.path.getctime)\n", "                        new_filename = f\"{ins_name}_{ct_name}.csv\"\n", "                        new_path = os.path.join(DOWNLOAD_DIR, new_filename)\n", "                        \n", "                        if latest_file != new_path:\n", "                            os.rename(latest_file, new_path)\n", "                        print(f\"    ✅ 下载成功: {new_filename}\")\n", "                    else:\n", "                        print(f\"    ❌ 未检测到下载文件\")\n", "                except Exception as e:\n", "                    print(f\"    ⚠️ 检查下载出错: {e}\")\n", "            else:\n", "                print(f\"    ❌ 所有导出方法都失败，跳过 {ins_name} — {ct_name}\")\n", "            \n", "            # 关闭标签页，返回主窗口\n", "            driver.close()\n", "            driver.switch_to.window(main_handle)\n", "            wait.until(EC.element_to_be_clickable((By.ID, \"ctl00_ContentPlaceHolder1_lstReportingPeriod_MoveAllRight\")))\n", "            print(f\"    ✔ 完成：{ins_name} — {ct_name}\")\n", "\n", "finally:\n", "    driver.quit()\n", "    print(\"\\n🎉 程序执行完成！\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d7e9c242-2bdd-4923-99dd-9a263a128714", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5a9f74b5-09b1-4bd0-9693-c62fbb81554a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "71e20226", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 诊断模式：测试 21ST CENTURY CENTENNIAL INSURANCE COMPANY - Desoto\n", "🌐 打开网站...\n", "⚙️ 配置查询条件...\n", "🏢 选择保险公司: 21ST CENTURY CENTENNIAL INSURANCE COMPANY\n", "✅ 全选相关选项...\n", "📍 选择县区: <PERSON><PERSON>\n", "🔍 搜索前窗口数量: 1\n", "🔍 当前URL: https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx\n", "🔍 点击搜索按钮...\n", "🔍 主窗口句柄: 1BF0F3D1BC4C7983854EA2DEAD175105\n", "✅ 已点击搜索按钮，等待响应...\n", "⏱️ 第1秒 - 窗口数: 2, URL: https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx\n", "✅ 检测到新窗口打开！\n", "🔍 新窗口句柄: 5989453130FB66B84703B3B94C3D666A\n", "🔍 新窗口URL: https://apps.fldfs.com/QSRNG/Reports/ReportViewer.aspx\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📋 按回车键继续关闭浏览器... \n"]}, {"name": "stdout", "output_type": "stream", "text": ["🎉 诊断完成！\n"]}], "source": ["# 🔧 问题诊断版本：分析新窗口打开问题\n", "# 这个版本会帮助我们了解为什么新窗口没有打开\n", "\n", "import time\n", "import os\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait, Select\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 配置\n", "CHROME_DRIVER_PATH = \"/Users/<USER>/Documents/赚点小银子/编程/400/chromedriver\"\n", "DOWNLOAD_DIR = \"/Users/<USER>/Documents/赚点小银子/编程/400/QSRNG_reports\"\n", "\n", "# 测试数据 - 只测试一家公司的一个县\n", "test_insurer = (\"101268\", \"21ST CENTURY CENTENNIAL INSURANCE COMPANY\")\n", "test_county = (\"34\", \"Desoto\")\n", "\n", "print(f\"🔍 诊断模式：测试 {test_insurer[1]} - {test_county[1]}\")\n", "\n", "# 启动 Selenium ChromeDriver\n", "service = Service(CHROME_DRIVER_PATH)\n", "options = webdriver.ChromeOptions()\n", "options.add_experimental_option(\"prefs\", {\n", "    \"download.default_directory\": DOWNLOAD_DIR,\n", "    \"download.prompt_for_download\": <PERSON>als<PERSON>,\n", "})\n", "\n", "driver = webdriver.Chrome(service=service, options=options)\n", "driver.execute_cdp_cmd(\"Page.setDownloadBehavior\", {\"behavior\": \"allow\", \"downloadPath\": DOWNLOAD_DIR})\n", "wait = WebDriverWait(driver, 30)\n", "\n", "try:\n", "    # 1. 打开查询条件页面\n", "    print(\"🌐 打开网站...\")\n", "    driver.get(\"https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx\")\n", "    driver.maximize_window()\n", "    time.sleep(3)  # 等待页面完全加载\n", "\n", "    # 2. 基本设置\n", "    print(\"⚙️ 配置查询条件...\")\n", "    wait.until(EC.element_to_be_clickable((By.ID, \"ctl00_ContentPlaceHolder1_radReportType_2\"))).click()\n", "    time.sleep(1)\n", "    \n", "    Select(wait.until(EC.presence_of_element_located((By.ID, \"ctl00_ContentPlaceHolder1_ddlFilingType\")))).select_by_value(\"1\")\n", "    time.sleep(1)\n", "    \n", "    wait.until(EC.element_to_be_clickable((By.ID, \"ctl00_ContentPlaceHolder1_lstReportingPeriod_MoveAllRight\"))).click()\n", "    time.sleep(1)\n", "\n", "    # 3. 选择保险公司\n", "    print(f\"🏢 选择保险公司: {test_insurer[1]}\")\n", "    driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstInsurers_MoveLeft\").click()\n", "    time.sleep(2)\n", "    driver.find_element(By.CSS_SELECTOR, f\"#ctl00_ContentPlaceHolder1_lstInsurers option[value='{test_insurer[0]}']\").click()\n", "    driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstInsurers_MoveRight\").click()\n", "    time.sleep(2)\n", "\n", "    # 4. 全选选项\n", "    print(\"✅ 全选相关选项...\")\n", "    driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstPolicyType_MoveAllRight\").click()\n", "    time.sleep(2)\n", "    driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstDataElements_MoveAllRight\").click()\n", "    time.sleep(2)\n", "\n", "    # 5. 选择县区\n", "    print(f\"📍 选择县区: {test_county[1]}\")\n", "    driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstCounties_MoveLeft\").click()\n", "    time.sleep(2)\n", "    driver.find_element(By.CSS_SELECTOR, f\"#ctl00_ContentPlaceHolder1_lstCounties option[value='{test_county[0]}']\").click()\n", "    time.sleep(2)\n", "    driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_lstCounties_MoveRight\").click()\n", "    time.sleep(2)\n", "\n", "    # 6. 点击搜索前检查当前窗口数量\n", "    current_windows = len(driver.window_handles)\n", "    print(f\"🔍 搜索前窗口数量: {current_windows}\")\n", "    print(f\"🔍 当前URL: {driver.current_url}\")\n", "    \n", "    # 7. 点击搜索按钮\n", "    print(\"🔍 点击搜索按钮...\")\n", "    search_button = driver.find_element(By.ID, \"ctl00_ContentPlaceHolder1_btnSearch\")\n", "    main_handle = driver.current_window_handle\n", "    print(f\"🔍 主窗口句柄: {main_handle}\")\n", "    \n", "    # 点击搜索\n", "    search_button.click()\n", "    print(\"✅ 已点击搜索按钮，等待响应...\")\n", "    \n", "    # 8. 监控窗口变化\n", "    for i in range(30):  # 等待30秒\n", "        current_windows = len(driver.window_handles)\n", "        current_url = driver.current_url\n", "        print(f\"⏱️ 第{i+1}秒 - 窗口数: {current_windows}, URL: {current_url}\")\n", "        \n", "        if current_windows > 1:\n", "            print(\"✅ 检测到新窗口打开！\")\n", "            new_handle = [h for h in driver.window_handles if h != main_handle][0]\n", "            print(f\"🔍 新窗口句柄: {new_handle}\")\n", "            driver.switch_to.window(new_handle)\n", "            print(f\"🔍 新窗口URL: {driver.current_url}\")\n", "            break\n", "        elif current_url != \"https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx\":\n", "            print(f\"⚠️ 页面跳转到: {current_url}\")\n", "            # 页面在同一窗口中跳转，不是新窗口\n", "            break\n", "        \n", "        time.sleep(1)\n", "    else:\n", "        print(\"❌ 超时：30秒内没有检测到新窗口或页面跳转\")\n", "        print(\"🔍 最终调试信息:\")\n", "        print(f\"   - 窗口数量: {len(driver.window_handles)}\")\n", "        print(f\"   - 当前URL: {driver.current_url}\")\n", "        print(f\"   - 页面标题: {driver.title}\")\n", "        \n", "        # 保存页面源码用于调试\n", "        with open(\"debug_search_result.html\", \"w\", encoding=\"utf-8\") as f:\n", "            f.write(driver.page_source)\n", "        print(\"💾 页面源码已保存到 debug_search_result.html\")\n", "        \n", "        raise TimeoutException(\"搜索后没有打开新窗口或跳转页面\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 发生错误: {e}\")\n", "    print(f\"🔍 当前窗口数量: {len(driver.window_handles)}\")\n", "    print(f\"🔍 当前URL: {driver.current_url}\")\n", "    \n", "finally:\n", "    # 不要立即关闭，让用户能看到结果\n", "    input(\"📋 按回车键继续关闭浏览器...\")\n", "    driver.quit()\n", "    print(\"🎉 诊断完成！\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "0fff5bce", "metadata": {}, "outputs": [], "source": ["# 🚀 增强版主程序：包含智能重试机制\n", "# 解决偶发的新窗口超时问题\n", "\n", "import time\n", "import os\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait, Select\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException\n", "\n", "# 配置\n", "CHROME_DRIVER_PATH = \"/Users/<USER>/Documents/赚点小银子/编程/400/chromedriver\"\n", "DOWNLOAD_DIR = \"/Users/<USER>/Documents/赚点小银子/编程/400/QSRNG_reports\"\n", "\n", "# 智能窗口等待函数\n", "def wait_for_new_window_with_retry(driver, main_handle, max_attempts=3, timeout_per_attempt=45):\n", "    \"\"\"智能等待新窗口打开，包含重试机制\"\"\"\n", "    for attempt in range(max_attempts):\n", "        try:\n", "            print(f\"    🔄 尝试等待新窗口 (第{attempt+1}/{max_attempts}次)\")\n", "            \n", "            for i in range(timeout_per_attempt):\n", "                current_windows = len(driver.window_handles)\n", "                current_url = driver.current_url\n", "                \n", "                if current_windows > 1:\n", "                    print(f\"    ✅ 第{i+1}秒检测到新窗口！\")\n", "                    new_handle = [h for h in driver.window_handles if h != main_handle][0]\n", "                    driver.switch_to.window(new_handle)\n", "                    return True\n", "                elif current_url != \"https://apps.fldfs.com/QSRNG/Reports/ReportCriteria.aspx\":\n", "                    print(f\"    ⚠️ 页面跳转到: {current_url}\")\n", "                    return True\n", "                \n", "                if i % 5 == 0:  # 每5秒显示一次进度\n", "                    print(f\"    ⏳ 等待中... ({i+1}/{timeout_per_attempt}秒)\")\n", "                \n", "                time.sleep(1)\n", "            \n", "            print(f\"    ⚠️ 第{attempt+1}次尝试超时\")\n", "            if attempt < max_attempts - 1:\n", "                print(f\"    🔄 准备重试...\")\n", "                time.sleep(2)\n", "                \n", "        except Exception as e:\n", "            print(f\"    ❌ 第{attempt+1}次尝试出错: {e}\")\n", "            if attempt < max_attempts - 1:\n", "                time.sleep(2)\n", "    \n", "    return False\n", "\n", "print(\"🚀 准备启动增强版程序...\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "591d99a0-4a20-4e78-9ade-8dc65ca5df9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}