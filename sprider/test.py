import requests
import json
import gzip
from io import BytesIO

# 预定义的数据负载
def get_payload_type_1():
    """第一种数据负载 - 完整的保险数据表格"""
    return [
        "Direct premium written for policies in force that include wind coverage",
        "Direct premium written for policies in force that exclude wind coverage",
        "Number of policies transferred to other insurers",
        "Number of policies received from other insurers",
        "Claims Opened",
        "Claims Closed",
        "Claims Pending",
        "Claims with Alt Dispute Resolution",
        "Claims with Mediation",
        "Claims with Arbitration",
        "Claims with Appraisal",
        "Claims with Sink Hole Eval",
        "Claims with Settlement",
        "Claims with Other",
        "Commercial Residential - Allied Lines (Condo Associations Only)",
        "140", "5", "6", "1", "8", "1,209,744,508", "4,914,888",
        "Commercial Residential - Allied Lines (Excl Condo Associations)",
        "47", "2", "3", "40,255,063", "248,782",
        # ... 这里可以继续添加更多数据
    ]

def get_payload_type_2():
    """第二种数据负载 - 时间序列数据"""
    return [
        "Direct premium written for policies in force that include wind coverage",
        "Direct premium written for policies in force that exclude wind coverage",
        "Number of policies transferred to other insurers",
        "Number of policies received from other insurers",
        "Claims Opened",
        "Claims Closed",
        "Claims Pending",
        "Claims with Alt Dispute Resolution",
        "Claims with Mediation",
        "Claims with Arbitration",
        "Claims with Appraisal",
        "Claims with Sink Hole Eval",
        "Claims with Settlement",
        "Claims with Other",
        "Commercial Residential - Dwelling/Fire (Excl Condo Associations)",
        "4",
        "3,231,021",
        "3,028",
        "Total",
        "Period Ending  6/30/2009",
        "Policies in force",
        "Number of policies canceled",
        "Number of policies nonrenewed",
        "Number of policies canceled due to hurricane risk",
        "Number of policies nonrenewed due to hurricane risk",
        "Number of new policies written",
        "Policies in force that exclude wind coverage",
        "Total $ value of exposure for policies in force that include wind coverage",
        "Total premiums written",
        "Policies in force that include wind coverage",
        "Total $ value of exposure for policies in force that exclude wind coverage",
        "3,259,887",
        "2,883",
        "Period Ending  9/30/2009",
        "Period Ending  12/31/2009",
        "2,850",
        "Period Ending  3/31/2010",
        "Period Ending  6/30/2010",
        "1",
        "2,915,247",
        "1,965",
        "Period Ending  9/30/2010",
        "3",
        "2,284,287",
        "1,452",
        "Period Ending  12/31/2010",
        "2,232,861",
        "1,405",
        "Page Number 1 / 2",
        "Data is reported by each company to the Office and has not been audited."
    ]

def request_google_translate_api(data_payload=None, source_lang="en", target_lang="zh-CN"):
    """
    请求Google翻译API接口获取数据

    Args:
        data_payload: 要翻译的数据数组，如果为None则使用默认数据
        source_lang: 源语言，默认为英文
        target_lang: 目标语言，默认为中文
    """
    # API配置
    url = "https://translate-pa.googleapis.com/v1/translateHtml"
    api_key = "AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520"

    # 请求头
    headers = {
        'accept': '*/*',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json+protobuf',
        'origin': 'https://apps.fldfs.com',
        'referer': 'https://apps.fldfs.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-client-data': 'CJa2yQEIprbJAQipncoBCLXuygEIk6HLAQiRo8sBCIagzQEIk/bOAQ==',
        'x-goog-api-key': api_key
    }

    # 构建请求数据 - 支持动态数据负载
    if data_payload is None:
        # 默认数据负载（第一个请求的数据）
        data_payload = [
                "Direct premium written for policies in force that include wind coverage",
                "Direct premium written for policies in force that exclude wind coverage",
                "Number of policies transferred to other insurers",
                "Number of policies received from other insurers",
                "Claims Opened",
                "Claims Closed",
                "Claims Pending",
                "Claims with Alt Dispute Resolution",
                "Claims with Mediation",
                "Claims with Arbitration",
                "Claims with Appraisal",
                "Claims with Sink Hole Eval",
                "Claims with Settlement",
                "Claims with Other",
                "Commercial Residential - Allied Lines (Condo Associations Only)",
                "140",
                "5",
                "6",
                "1",
                "8",
                "1,209,744,508",
                "4,914,888",
                "Commercial Residential - Allied Lines (Excl Condo Associations)",
                "47",
                "2",
                "3",
                "40,255,063",
                "248,782",
                "Commercial Residential - CMP (Condo Associations Only)",
                "36",
                "21",
                "218,633,830",
                "1,412,579",
                "15",
                "99,838,817",
                "1,212,250",
                "200,329",
                "Commercial Residential - CMP (Excl Condo Associations)",
                "632",
                "14",
                "65",
                "75",
                "117,664,403",
                "769,317",
                "557",
                "28,261,472",
                "690,163",
                "79,154",
                "Commercial Residential - Dwelling/Fire (Condo Associations Only)",
                "29",
                "4",
                "121,420,700",
                "873,259",
                "25",
                "78,688,000",
                "802,367",
                "70,892",
                "Commercial Residential - Dwelling/Fire (Excl Condo Associations)",
                "30",
                "51,083,400",
                "383,825",
                "45,000",
                "383,567",
                "258",
                "Personal Residential - Allied Lines",
                "7,847",
                "227",
                "1,158",
                "648",
                "34",
                "1,457,826,862",
                "8,451,361",
                "7,813",
                "4,784,645",
                "8,433,446",
                "17,915",
                "Personal Residential - Condominium Unit Owners",
                "8,849",
                "184",
                "505",
                "2,503",
                "575,773,997",
                "4,842,478",
                "6,346",
                "197,872,453",
                "3,909,620",
                "932,858",
                "138",
                "164",
                "Personal Residential - Dwelling/Fire",
                "9,398",
                "355",
                "169",
                "643",
                "2,072",
                "1,286,593,850",
                "9,304,016",
                "7,326",
                "487,967,576",
                "7,703,919",
                "1,600,098",
                "100",
                "388",
                "Personal Residential - Farmowners",
                "953,700",
                "3,525",
                "Personal Residential - Homeowners (Excl Tenant and Condo) - Owner Occupied",
                "34,584",
                "990",
                "507",
                "1,508",
                "2,338",
                "12,182,554,127",
                "59,020,178",
                "32,246",
                "1,074,347,589",
                "56,638,865",
                "2,381,314",
                "84",
                "135",
                "Personal Residential - Mobile Homeowners",
                "4,929",
                "167",
                "160",
                "333",
                "281",
                "315,088,588",
                "4,607,828",
                "4,648",
                "13,737,253",
                "4,491,156",
                "116,672",
                "9",
                "10",
                "Personal Residential - Tenants",
                "3,716",
                "357",
                "33",
                "520",
                "20",
                "130,865,328",
                "915,249",
                "3,696",
                "955,420",
                "910,288",
                "4,961",
                "12",
                "Total",
                "70,239",
                "2,380",
                "2,292",
                "4,179",
                "7,349",
                "17,708,458,355",
                "95,747,285",
                "62,890",
                "1,986,498,225",
                "90,342,835",
                "5,404,450",
                "341",
                "709",
                "Period Ending  6/30/2009",
                "Policies in force",
                "Number of policies canceled",
                "Number of policies nonrenewed",
                "Number of policies canceled due to hurricane risk",
                "Number of policies nonrenewed due to hurricane risk",
                "Number of new policies written",
                "Policies in force that exclude wind coverage",
                "Total $ value of exposure for policies in force that include wind coverage",
                "Total premiums written",
                "Policies in force that include wind coverage",
                "Total $ value of exposure for policies in force that exclude wind coverage",
                "105",
                "898,006,386",
                "3,493,400",
                "39",
                "36,360,183",
                "218,989",
                "226,320,985",
                "1,407,064",
                "16",
                "99,165,949",
                "1,227,388",
                "179,676",
                "594",
                "23",
                "17",
                "64",
                "113,251,172",
                "732,770",
                "530",
                "26,620,584",
                "631,902",
                "100,868",
                "32",
                "112,174,600",
                "824,489",
                "27",
                "82,870,800",
                "746,041",
                "78,448",
                "57,049,300",
                "392,342",
                "392,084",
                "6,905",
                "225",
                "2,495",
                "1,776",
                "37",
                "1,258,379,848",
                "6,877,278",
                "6,868",
                "5,199,894",
                "6,859,898",
                "17,380",
                "8,883",
                "262",
                "273",
                "561",
                "2,445",
                "584,225,635",
                "4,871,193",
                "6,438",
                "192,508,826",
                "3,963,322",
                "907,871",
                "62",
                "71",
                "9,255",
                "429",
                "206",
                "502",
                "1,975",
                "1,282,116,850",
                "9,340,550",
                "7,280",
                "469,150,109",
                "7,831,224",
                "1,509,327",
                "35,270",
                "1,010",
                "474",
                "2,156",
                "2,248",
                "12,600,460,352",
                "60,271,563",
                "33,022",
                "1,017,529,518",
                "58,025,609",
                "2,245,954",
                "31",
                "42",
                "4,936",
                "159",
                "185",
                "338",
                "243",
                "316,323,933",
                "4,507,096",
                "4,693",
                "12,079,286",
                "4,408,334",
                "98,762",
                "115",
                "121",
                "3,895",
                "407",
                "43",
                "18",
                "132,354,243",
                "921,232"
        ]

    # 构建完整的请求数据
    request_data = [
        [
            data_payload,
            source_lang,
            target_lang
        ],
        "te_lib"
    ]

    try:
        print("🚀 开始请求Google翻译API...")
        print(f"📡 请求URL: {url}")

        # 发送POST请求
        response = requests.post(
            url,
            headers=headers,
            json=request_data,
            timeout=30
        )

        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ 请求成功!")

            # 先尝试直接获取响应内容
            try:
                # 检查响应内容的前几个字节
                content_preview = response.content[:10]
                print(f"📄 响应内容预览: {content_preview}")

                # 尝试多种方式处理响应
                response_data = None

                # 方法1: 直接使用response.text (优先)
                try:
                    response_text = response.text
                    print("📄 直接获取的响应数据:")
                    print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
                    response_data = response_text
                except Exception as e:
                    print(f"❌ 直接获取响应文本失败: {e}")
                    response_data = None

                # 方法2: 如果标记为gzip，尝试解压
                if response.headers.get('content-encoding') == 'gzip' and response_data is None:
                    print("🗜️ 尝试gzip解压...")
                    try:
                        compressed_data = BytesIO(response.content)
                        with gzip.GzipFile(fileobj=compressed_data) as f:
                            decompressed_data = f.read().decode('utf-8')
                        print("📄 gzip解压后的响应数据:")
                        print(decompressed_data[:500] + "..." if len(decompressed_data) > 500 else decompressed_data)
                        response_data = decompressed_data
                    except Exception as e:
                        print(f"❌ gzip解压失败: {e}")

                # 方法3: 尝试直接解码字节内容
                if response_data is None:
                    print("🔧 尝试直接解码字节内容...")
                    try:
                        response_data = response.content.decode('utf-8')
                        print("� 直接解码的响应数据:")
                        print(response_data[:500] + "..." if len(response_data) > 500 else response_data)
                    except Exception as e:
                        print(f"❌ 直接解码失败: {e}")
                        # 如果UTF-8解码失败，尝试其他编码
                        try:
                            response_data = response.content.decode('latin-1')
                            print("📄 使用latin-1解码的响应数据:")
                            print(response_data[:500] + "..." if len(response_data) > 500 else response_data)
                        except Exception as e2:
                            print(f"❌ latin-1解码也失败: {e2}")
                            response_data = str(response.content)

                # 尝试解析JSON
                if response_data:
                    try:
                        json_data = json.loads(response_data)
                        print("📊 成功解析JSON数据:")
                        print(json.dumps(json_data, indent=2, ensure_ascii=False))
                        return json_data
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print("📄 返回原始响应内容")
                        return response_data
                else:
                    print("❌ 无法获取响应数据")
                    return None

            except Exception as e:
                print(f"❌ 处理响应时发生错误: {e}")
                return None
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return None


def save_response_to_file(data, filename="google_translate_response.json"):
    """
    将响应数据保存到文件
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            if isinstance(data, dict) or isinstance(data, list):
                json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                f.write(str(data))
        print(f"💾 数据已保存到文件: {filename}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")


def test_different_payloads():
    """测试不同的数据负载"""
    print("🔧 Google翻译API请求工具")
    print("=" * 50)

    # 测试第一种数据负载
    print("\n📊 测试第一种数据负载（完整保险数据）...")
    result1 = request_google_translate_api(data_payload=get_payload_type_1())
    if result1:
        save_response_to_file(result1, "translate_result_type1.json")
        print("✅ 第一种数据负载测试成功!")

    # 测试第二种数据负载
    print("\n📊 测试第二种数据负载（时间序列数据）...")
    result2 = request_google_translate_api(data_payload=get_payload_type_2())
    if result2:
        save_response_to_file(result2, "translate_result_type2.json")
        print("✅ 第二种数据负载测试成功!")

    return result1, result2

def test_selection_combinations():
    """测试不同的保险公司和县选择组合"""
    print("🔧 测试不同选择组合对应的数据负载")
    print("=" * 60)

    test_cases = [
        ("All", "45", "所有保险公司 + Bradford县"),
        ("101109", "All", "21ST CENTURY PREMIER + 所有县"),
        ("101109", "45", "21ST CENTURY PREMIER + Bradford县"),
        ("All", "All", "所有保险公司 + 所有县")
    ]

    for insurer, county, description in test_cases:
        print(f"\n🎯 测试场景: {description}")
        print(f"   保险公司选择: {insurer}")
        print(f"   县选择: {county}")

        # 获取对应的数据负载
        payload = get_payload_by_selection(insurer, county)

        # 显示数据负载的前几项
        print(f"   数据负载预览: {payload[:3]}...")
        print(f"   数据项总数: {len(payload)}")

        # 可选：实际请求API（注释掉以避免过多请求）
        # result = request_google_translate_api(data_payload=payload)
        # if result:
        #     filename = f"result_{insurer}_{county}.json"
        #     save_response_to_file(result, filename)
        #     print(f"   ✅ 结果已保存到: {filename}")

        print("   " + "-" * 50)

def create_custom_payload(insurance_type, county_data):
    """
    创建自定义数据负载

    Args:
        insurance_type: 保险类型，如 "Commercial Residential - Allied Lines"
        county_data: 县数据列表
    """
    base_headers = [
        "Direct premium written for policies in force that include wind coverage",
        "Direct premium written for policies in force that exclude wind coverage",
        "Number of policies transferred to other insurers",
        "Number of policies received from other insurers",
        "Claims Opened",
        "Claims Closed",
        "Claims Pending"
    ]

    custom_payload = base_headers + [insurance_type] + county_data
    return custom_payload

def get_payload_by_selection(insurer_selection, county_selection):
    """
    根据保险公司和县的选择生成对应的数据负载

    Args:
        insurer_selection: 保险公司选择 ("All" 或 具体的保险公司ID)
        county_selection: 县选择 ("All" 或 具体的县ID)

    Returns:
        对应的数据负载数组
    """

    if insurer_selection == "All" and county_selection != "All":
        # 情况1: 所有保险公司 + 特定县 = 详细的保险类型数据
        print(f"📊 生成数据：所有保险公司在县 {county_selection} 的详细数据")
        return get_payload_type_1()  # 返回详细的多保险类型数据

    elif insurer_selection != "All" and county_selection == "All":
        # 情况2: 特定保险公司 + 所有县 = 时间序列数据
        print(f"📊 生成数据：保险公司 {insurer_selection} 在所有县的时间序列数据")
        return get_payload_type_2()  # 返回时间序列数据

    elif insurer_selection != "All" and county_selection != "All":
        # 情况3: 特定保险公司 + 特定县 = 简化数据
        print(f"📊 生成数据：保险公司 {insurer_selection} 在县 {county_selection} 的数据")
        return [
            "Direct premium written for policies in force that include wind coverage",
            "Direct premium written for policies in force that exclude wind coverage",
            "Claims Opened",
            "Claims Closed",
            "Claims Pending",
            "Total premiums written",
            "Policies in force",
            "Period Ending 6/30/2009"
        ]

    else:
        # 情况4: 所有保险公司 + 所有县 = 汇总数据
        print("📊 生成数据：所有保险公司在所有县的汇总数据")
        return [
            "Total Direct premium written",
            "Total Claims Opened",
            "Total Claims Closed",
            "Total Policies in force",
            "Grand Total"
        ]

# 县ID映射
COUNTY_MAP = {
    "45": "Bradford",
    "19": "Brevard",
    "58": "Calhoun",
    "53": "Charlotte",
    "47": "Citrus",
    "48": "Clay",
    "64": "Collier",
    "01": "Dade",
    "34": "Desoto",
    "54": "Dixie",
    "02": "Duval",
    "09": "Escambia",
    "61": "Flagler",
    "59": "Franklin",
    "21": "Gadsden",
    "55": "Gilchrist",
    "60": "Glades",
    "66": "Gulf",
    "56": "Hamilton",
    "30": "Hardee",
    "49": "Hendry",
    "40": "Hernando",
    "27": "Highlands",
    "03": "Hillsborough",
    "51": "Holmes",
    "32": "Indian River",
    "25": "Jackson",
    "46": "Jefferson",
    "62": "Lafayette",
    "12": "Lake"
}

# 保险公司ID映射（部分）
INSURER_MAP = {
    "101109": "21ST CENTURY PREMIER INSURANCE COMPANY",
    "200873": "ACCIDENT FUND INSURANCE COMPANY OF AMERICA",
    "201865": "ACE INSURANCE COMPANY OF THE MIDWEST",
    "100634": "ACE PROPERTY AND CASUALTY INSURANCE COMPANY",
    "101736": "ACSTAR INSURANCE COMPANY",
    "105484": "ADDISON INSURANCE COMPANY"
}

if __name__ == "__main__":
    print("🔧 Google翻译API请求工具")
    print("=" * 50)
    print("请选择测试模式:")
    print("1. 使用默认数据负载")
    print("2. 测试两种不同的数据负载")
    print("3. 使用第二种数据负载（新的）")
    print("4. 测试选择组合规律")
    print("5. 自定义保险公司和县选择")

    choice = input("请输入选择 (1/2/3/4/5): ").strip()

    if choice == "1":
        # 使用默认数据负载
        result = request_google_translate_api()
        if result:
            save_response_to_file(result, "google_translate_response.json")
            print("\n✅ 任务完成!")
        else:
            print("\n❌ 任务失败!")

    elif choice == "2":
        # 测试两种不同的数据负载
        test_different_payloads()

    elif choice == "3":
        # 使用第二种数据负载
        result = request_google_translate_api(data_payload=get_payload_type_2())
        if result:
            save_response_to_file(result, "translate_result_new_payload.json")
            print("\n✅ 新数据负载测试完成!")
        else:
            print("\n❌ 任务失败!")

    elif choice == "4":
        # 测试选择组合规律
        test_selection_combinations()

    elif choice == "5":
        # 自定义保险公司和县选择
        print("\n🎯 自定义选择")
        print("保险公司选项:")
        print("  - All: 所有保险公司")
        print("  - 101109: 21ST CENTURY PREMIER INSURANCE COMPANY")
        print("  - 200873: ACCIDENT FUND INSURANCE COMPANY OF AMERICA")
        print("  - 其他ID...")

        insurer = input("请输入保险公司选择 (All 或 具体ID): ").strip()

        print("\n县选项:")
        print("  - All: 所有县")
        print("  - 45: Bradford")
        print("  - 19: Brevard")
        print("  - 58: Calhoun")
        print("  - 其他ID...")

        county = input("请输入县选择 (All 或 具体ID): ").strip()

        # 生成对应的数据负载
        payload = get_payload_by_selection(insurer, county)

        # 请求API
        result = request_google_translate_api(data_payload=payload)
        if result:
            filename = f"custom_result_{insurer}_{county}.json"
            save_response_to_file(result, filename)
            print(f"\n✅ 自定义请求完成! 结果保存到: {filename}")
        else:
            print("\n❌ 任务失败!")

    else:
        print("❌ 无效选择!")

    print("\n📋 使用说明:")
    print("- 不同的保险公司或县会有不同的数据内容")
    print("- 数据负载的结构保持不变，只是数组内容不同")
    print("- 你可以通过修改 get_payload_type_1() 或 get_payload_type_2() 函数来自定义数据")
    print("- 或者使用 create_custom_payload() 函数创建特定的数据负载")